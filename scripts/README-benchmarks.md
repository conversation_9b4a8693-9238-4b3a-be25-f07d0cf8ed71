# Multi-Repository Benchmark Runner

This document explains how to use the updated benchmark runner script that supports multi-repository microservice architectures.

## Overview

The `run-benchmarks.sh` script has been enhanced to work with microservices located in separate Git repositories. It supports flexible configuration through environment variables and configuration files.

## Quick Start

### 1. For Single Repository Setup (Current Directory)
If all your services are in the current directory with `coupon-` prefixes, the script works out of the box:

```bash
./scripts/run-benchmarks.sh
```

### 2. For Multi-Repository Setup

#### Option A: Using Environment Variables
Set environment variables for each service path:

```bash
export AUTH_SERVICE_PATH="/path/to/auth-service-repo"
export USER_SERVICE_PATH="/path/to/user-service-repo"
export VOUCHER_SERVICE_PATH="/path/to/voucher-service-repo"
# ... set other service paths

./scripts/run-benchmarks.sh
```

#### Option B: Using Configuration File (Recommended)
1. Copy the example configuration file:
   ```bash
   cp scripts/benchmark-config.env.example scripts/benchmark-config.env
   ```

2. Edit `scripts/benchmark-config.env` with your service repository paths:
   ```bash
   # Example configuration
   AUTH_SERVICE_PATH="/Users/<USER>/microservices/auth-service"
   USER_SERVICE_PATH="/Users/<USER>/microservices/user-service"
   VOUCHER_SERVICE_PATH="/Users/<USER>/microservices/voucher-service"
   # ... etc
   ```

3. Run the benchmarks:
   ```bash
   ./scripts/run-benchmarks.sh
   ```

## Configuration Options

### Service Path Configuration

The script supports the following environment variables for service paths:

- `AUTH_SERVICE_PATH` - Path to auth service repository
- `USER_SERVICE_PATH` - Path to user service repository  
- `VOUCHER_SERVICE_PATH` - Path to voucher service repository
- `PRODUCT_SERVICE_PATH` - Path to product service repository
- `ORDER_SERVICE_PATH` - Path to order service repository
- `NOTIFICATION_SERVICE_PATH` - Path to notification service repository
- `API_GATEWAY_PATH` - Path to API gateway repository

### Benchmark Configuration

- `BENCHMARK_DURATION` - Duration for each benchmark (default: "30s")
- `BENCHMARK_CONCURRENCY` - Number of concurrent goroutines (default: "10")
- `BENCHMARK_TIMEOUT` - Test timeout (default: "30m")
- `BENCHMARK_CONFIG_FILE` - Path to configuration file (default: "scripts/benchmark-config.env")

### Configuration File Format

Create a `scripts/benchmark-config.env` file with the following format:

```bash
# Service repository paths
AUTH_SERVICE_PATH="/absolute/path/to/auth-service"
USER_SERVICE_PATH="/absolute/path/to/user-service"
VOUCHER_SERVICE_PATH="/absolute/path/to/voucher-service"
PRODUCT_SERVICE_PATH="/absolute/path/to/product-service"
ORDER_SERVICE_PATH="/absolute/path/to/order-service"
NOTIFICATION_SERVICE_PATH="/absolute/path/to/notification-service"
API_GATEWAY_PATH="/absolute/path/to/api-gateway"

# Optional: Override benchmark settings
BENCHMARK_DURATION="60s"
BENCHMARK_CONCURRENCY="20"
```

## Usage Examples

### Basic Usage
```bash
# Run with default settings
./scripts/run-benchmarks.sh

# Run quick benchmarks (10s duration)
./scripts/run-benchmarks.sh quick

# Run load benchmarks (60s duration, 20 concurrency)
./scripts/run-benchmarks.sh load

# Run stress benchmarks (120s duration, 50 concurrency)  
./scripts/run-benchmarks.sh stress
```

### Custom Configuration
```bash
# Use custom config file
BENCHMARK_CONFIG_FILE="/path/to/my-config.env" ./scripts/run-benchmarks.sh

# Override specific service path
AUTH_SERVICE_PATH="/custom/auth/path" ./scripts/run-benchmarks.sh

# Custom benchmark settings
BENCHMARK_DURATION="2m" BENCHMARK_CONCURRENCY="50" ./scripts/run-benchmarks.sh
```

## Directory Structure Requirements

Each service repository should have the following structure:
```
service-repository/
├── benchmark/
│   ├── *_test.go          # Benchmark test files
│   └── ...
├── go.mod
├── go.sum
└── ...
```

## Troubleshooting

### Common Issues

1. **Service directory not found**
   - Verify the path in your configuration file or environment variable
   - Ensure the path points to the root of the service repository
   - Check that the directory exists and is accessible

2. **No benchmark directory found**
   - Ensure each service has a `benchmark/` directory
   - Verify the benchmark directory contains `*_test.go` files

3. **Go command not found**
   - Ensure Go is installed and in your PATH
   - Verify you can run `go version` successfully

4. **Permission denied**
   - Check file permissions on the script: `chmod +x scripts/run-benchmarks.sh`
   - Ensure you have read access to all service directories

### Debug Mode

The script prints service paths during execution. Check the output to verify correct paths:

```
[INFO] Service repository paths:
[INFO]   auth-service: /path/to/auth-service
[INFO]   user-service: /path/to/user-service
...
```

## Output

The script generates:
- Individual benchmark result files: `benchmark-results/{service}_benchmark_{timestamp}.txt`
- Summary report: `benchmark-results/benchmark_summary_{timestamp}.md`

Results are saved in the `benchmark-results/` directory relative to the script location.

## Integration with CI/CD

For automated benchmarking in CI/CD pipelines:

```bash
# Set paths via environment variables
export AUTH_SERVICE_PATH="/ci/workspace/auth-service"
export USER_SERVICE_PATH="/ci/workspace/user-service"
# ... set other paths

# Run benchmarks
./scripts/run-benchmarks.sh quick
```

Or use a configuration file committed to your repository:

```bash
# Use CI-specific config
BENCHMARK_CONFIG_FILE="ci/benchmark-config.env" ./scripts/run-benchmarks.sh
```
