# GitLab Benchmark Configuration Template
# This configuration is optimized for GitLab-hosted repositories
# Copy this file to 'benchmark-config.env' and customize for your GitLab setup

# =============================================================================
# GIT CONFIGURATION
# =============================================================================

# Enable automatic Git operations (clone/pull) before running benchmarks
GIT_AUTO_PULL="true"

# Base directory where repositories will be cloned/updated
# This directory will be created if it doesn't exist
GIT_BASE_DIR="/tmp/benchmark-repos"

# Git branch to checkout and test
GIT_BRANCH="main"

# =============================================================================
# GITLAB AUTHENTICATION (for private repositories)
# =============================================================================
# If your repositories are private, you need to provide authentication
# Option 1: Use GitLab Personal Access Token (recommended)
# GIT_USERNAME="your-gitlab-username"
# GIT_TOKEN="glpat-xxxxxxxxxxxxxxxxxxxx"

# Option 2: Use SSH (if you have SSH keys configured)
# Leave GIT_USERNAME and GIT_TOKEN empty and use SSH URLs below

# =============================================================================
# GITLAB REPOSITORY URLS
# =============================================================================
# Replace 'your-group' with your actual GitLab group/username
# Replace repository names with your actual repository names

# For HTTPS (use with username/token authentication)
AUTH_SERVICE_GIT_URL="https://gitlab.com/your-group/auth-service.git"
USER_SERVICE_GIT_URL="https://gitlab.com/your-group/user-service.git"
VOUCHER_SERVICE_GIT_URL="https://gitlab.com/your-group/voucher-service.git"
PRODUCT_SERVICE_GIT_URL="https://gitlab.com/your-group/product-service.git"
ORDER_SERVICE_GIT_URL="https://gitlab.com/your-group/order-service.git"
NOTIFICATION_SERVICE_GIT_URL="https://gitlab.com/your-group/notification-service.git"
API_GATEWAY_GIT_URL="https://gitlab.com/your-group/api-gateway.git"

# For SSH (use if you have SSH keys configured)
# AUTH_SERVICE_GIT_URL="**************:your-group/auth-service.git"
# USER_SERVICE_GIT_URL="**************:your-group/user-service.git"
# VOUCHER_SERVICE_GIT_URL="**************:your-group/voucher-service.git"
# PRODUCT_SERVICE_GIT_URL="**************:your-group/product-service.git"
# ORDER_SERVICE_GIT_URL="**************:your-group/order-service.git"
# NOTIFICATION_SERVICE_GIT_URL="**************:your-group/notification-service.git"
# API_GATEWAY_GIT_URL="**************:your-group/api-gateway.git"

# =============================================================================
# BENCHMARK SETTINGS (optional overrides)
# =============================================================================

# Benchmark duration (e.g., "30s", "1m", "2m30s")
BENCHMARK_DURATION="30s"

# Benchmark concurrency level (number of concurrent goroutines)
BENCHMARK_CONCURRENCY="10"

# Test timeout (e.g., "30m", "1h")
BENCHMARK_TIMEOUT="30m"

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Example 1: Public repositories with HTTPS
# GIT_AUTO_PULL="true"
# GIT_BASE_DIR="/tmp/benchmark-repos"
# GIT_BRANCH="main"
# AUTH_SERVICE_GIT_URL="https://gitlab.com/mycompany/microservices/auth-service.git"
# USER_SERVICE_GIT_URL="https://gitlab.com/mycompany/microservices/user-service.git"

# Example 2: Private repositories with Personal Access Token
# GIT_AUTO_PULL="true"
# GIT_BASE_DIR="/tmp/benchmark-repos"
# GIT_BRANCH="develop"
# GIT_USERNAME="john.doe"
# GIT_TOKEN="glpat-xxxxxxxxxxxxxxxxxxxx"
# AUTH_SERVICE_GIT_URL="https://gitlab.com/mycompany/private/auth-service.git"

# Example 3: SSH-based authentication
# GIT_AUTO_PULL="true"
# GIT_BASE_DIR="/home/<USER>/benchmark-repos"
# GIT_BRANCH="main"
# AUTH_SERVICE_GIT_URL="**************:mycompany/auth-service.git"

# Example 4: Mixed setup (some services from different groups)
# AUTH_SERVICE_GIT_URL="https://gitlab.com/team-auth/auth-service.git"
# USER_SERVICE_GIT_URL="https://gitlab.com/team-user/user-service.git"
# PRODUCT_SERVICE_GIT_URL="https://gitlab.com/team-catalog/product-service.git"

# =============================================================================
# GITLAB CI/CD INTEGRATION
# =============================================================================
# For GitLab CI/CD pipelines, you can use CI variables:
# GIT_USERNAME="${CI_REGISTRY_USER}"
# GIT_TOKEN="${CI_REGISTRY_PASSWORD}"
# GIT_BRANCH="${CI_COMMIT_REF_NAME}"

# =============================================================================
# TROUBLESHOOTING
# =============================================================================
# Common issues and solutions:
#
# 1. Authentication failed:
#    - Verify your GitLab username and token
#    - Ensure the token has 'read_repository' scope
#    - Check if repositories are accessible with your credentials
#
# 2. Repository not found:
#    - Verify the GitLab URLs are correct
#    - Check if you have access to the repositories
#    - Ensure group/project names are spelled correctly
#
# 3. SSH authentication issues:
#    - Verify SSH keys are configured in GitLab
#    - Test SSH connection: ssh -T **************
#    - Use SSH URLs instead of HTTPS URLs
#
# 4. Branch not found:
#    - Verify the branch name exists in all repositories
#    - Use 'main' or 'master' as appropriate for your repos
#
# 5. Permission denied:
#    - Check if GIT_BASE_DIR is writable
#    - Ensure you have permissions to create directories
