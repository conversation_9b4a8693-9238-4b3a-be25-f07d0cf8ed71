# Coupon System - Benchmark Testing Guide

This guide provides comprehensive instructions for running, analyzing, and interpreting benchmark tests for the Coupon microservice system.

## Overview

The benchmark testing framework provides:
- **Performance measurement** for all microservices
- **Load testing** with realistic scenarios
- **Regression detection** against historical data
- **Resource utilization** monitoring during tests
- **Integration with monitoring** stack for analysis

## Benchmark Framework Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Test Scenarios │───▶│  Benchmark      │───▶│   Prometheus    │
│                 │    │  Framework      │    │   Metrics       │
│ - gRPC Tests    │    │                 │    │                 │
│ - HTTP Tests    │    │ - Execution     │    │ - Collection    │
│ - DB Tests      │    │ - Measurement   │    │ - Storage       │
│ - Cache Tests   │    │ - Analysis      │    │ - Querying      │
│ - Load Tests    │    └─────────────────┘    └─────────────────┘
└─────────────────┘             │                       │
                                 ▼                       ▼
                        ┌─────────────────┐    ┌─────────────────┐
                        │     Results     │    │     Grafana     │
                        │   Reporting     │    │   Dashboards    │
                        │                 │    │                 │
                        │ - Statistics    │    │ - Visualization │
                        │ - Comparisons   │    │ - Trends        │
                        │ - Regressions   │    │ - Alerts        │
                        └─────────────────┘    └─────────────────┘
```

## Test Categories

### 1. gRPC Endpoint Tests

Tests individual gRPC methods with realistic payloads:

```bash
# User Service gRPC Tests
go test -bench=BenchmarkUserService_gRPC ./coupon-user-service/benchmark/

# Voucher Service gRPC Tests
go test -bench=BenchmarkVoucherService_gRPC ./coupon-voucher-service/benchmark/
```

**Metrics Measured:**
- Request latency (avg, p95, p99)
- Throughput (requests/second)
- Error rate
- Memory usage during test
- CPU utilization

### 2. Database Performance Tests

Tests database operations and query performance:

```bash
# Database query benchmarks
go test -bench=BenchmarkUserService_Database ./coupon-user-service/benchmark/

# Transaction benchmarks
go test -bench=BenchmarkVoucherService_Database ./coupon-voucher-service/benchmark/
```

**Test Types:**
- Single query performance
- Bulk operations
- Transaction performance
- Connection pool efficiency
- Index effectiveness

### 3. Cache Performance Tests

Tests Redis cache operations:

```bash
# Cache operation benchmarks
go test -bench=BenchmarkUserService_Cache ./coupon-user-service/benchmark/
```

**Operations Tested:**
- GET operations
- SET operations
- Cache hit/miss ratios
- Expiration handling
- Memory usage

### 4. HTTP API Tests

Tests REST API endpoints through the API Gateway:

```bash
# API Gateway HTTP benchmarks
go test -bench=BenchmarkAPIGateway_HTTP ./coupon-api-gateway/benchmark/
```

**Scenarios:**
- Authentication flows
- CRUD operations
- Search and filtering
- File uploads/downloads
- Rate limiting behavior

### 5. Load Testing

Simulates realistic user traffic patterns:

```bash
# Mixed load scenarios
go test -bench=BenchmarkUserService_ConcurrentLoad ./coupon-user-service/benchmark/

# Stress testing
go test -bench=BenchmarkAPIGateway_StressTest ./coupon-api-gateway/benchmark/
```

**Load Patterns:**
- Gradual ramp-up
- Sustained load
- Spike testing
- Mixed operation types
- Realistic user behavior

## Running Benchmarks

### Prerequisites

1. **Services Running**: Ensure all microservices are running
2. **Database Setup**: Test database with sample data
3. **Redis Running**: Cache service available
4. **Network Access**: Services accessible from test environment

### Individual Service Tests

```bash
# Run all benchmarks for a specific service
cd coupon-user-service
go test -bench=. -benchmem -timeout=30m ./benchmark/

# Run specific benchmark category
go test -bench=BenchmarkUserService_gRPC -benchmem ./benchmark/

# Run with custom parameters
go test -bench=BenchmarkUserService_Database -benchtime=30s -benchmem ./benchmark/
```

### All Services Test Suite

```bash
# Run comprehensive benchmark suite
./scripts/run-benchmarks.sh

# Run with custom configuration
BENCHMARK_DURATION=60s BENCHMARK_CONCURRENCY=20 ./scripts/run-benchmarks.sh
```

### Continuous Integration

```bash
# Add to CI pipeline
go test -bench=. -benchmem -short ./...

# Performance regression check
go test -bench=. -benchmem -count=5 ./benchmark/ | tee benchmark-results.txt
```

## Interpreting Results

### Benchmark Output Format

```
BenchmarkUserService_gRPC_CreateUser-8         1000    1205643 ns/op    2048 B/op    12 allocs/op
BenchmarkUserService_gRPC_GetUser-8            2000     856432 ns/op    1024 B/op     8 allocs/op
BenchmarkUserService_Database_CreateUser-8      500    2456789 ns/op    4096 B/op    24 allocs/op
```

**Columns Explained:**
- `BenchmarkName-8`: Test name and GOMAXPROCS
- `1000`: Number of iterations
- `1205643 ns/op`: Nanoseconds per operation
- `2048 B/op`: Bytes allocated per operation
- `12 allocs/op`: Memory allocations per operation

### Key Metrics

#### Performance Metrics
- **Latency**: Time per operation (lower is better)
- **Throughput**: Operations per second (higher is better)
- **Memory Usage**: Bytes allocated (lower is better)
- **Allocations**: Memory allocations (lower is better)

#### Quality Metrics
- **Error Rate**: Percentage of failed operations
- **Consistency**: Standard deviation of response times
- **Resource Efficiency**: CPU and memory utilization

### Performance Baselines

#### Acceptable Performance Targets

| Operation Type | Target Latency (p95) | Target Throughput | Max Error Rate |
|---------------|---------------------|-------------------|----------------|
| gRPC Calls    | < 200ms            | > 1000 ops/sec    | < 0.1%         |
| HTTP Requests | < 500ms            | > 500 ops/sec     | < 1%           |
| DB Queries    | < 100ms            | > 2000 ops/sec    | < 0.01%        |
| Cache Ops     | < 10ms             | > 10000 ops/sec   | < 0.1%         |

#### Warning Thresholds

| Metric | Warning | Critical |
|--------|---------|----------|
| Response Time | 2x baseline | 5x baseline |
| Throughput | 50% of baseline | 25% of baseline |
| Error Rate | > 1% | > 5% |
| Memory Usage | 2x baseline | 4x baseline |

## Performance Analysis

### Grafana Dashboard Analysis

1. **Access Benchmark Dashboard**: http://localhost:3000/d/benchmark-performance
2. **Select Time Range**: Choose appropriate time window
3. **Filter by Service**: Focus on specific services
4. **Analyze Trends**: Look for performance degradation

### Regression Detection

The framework automatically detects performance regressions:

```bash
# Check for regressions against last 24 hours
go test -bench=. -benchmem ./benchmark/ | ./scripts/check-regression.sh

# Compare with specific baseline
./scripts/compare-benchmarks.sh baseline.txt current.txt
```

### Performance Profiling

For detailed analysis of slow operations:

```bash
# CPU profiling
go test -bench=BenchmarkSlowOperation -cpuprofile=cpu.prof ./benchmark/
go tool pprof cpu.prof

# Memory profiling
go test -bench=BenchmarkMemoryIntensive -memprofile=mem.prof ./benchmark/
go tool pprof mem.prof

# Trace analysis
go test -bench=BenchmarkComplexOperation -trace=trace.out ./benchmark/
go tool trace trace.out
```

## Optimization Strategies

### Database Optimization

1. **Query Analysis**
   ```sql
   EXPLAIN ANALYZE SELECT * FROM users WHERE email = $1;
   ```

2. **Index Optimization**
   ```sql
   CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
   ```

3. **Connection Pool Tuning**
   ```go
   db.SetMaxOpenConns(25)
   db.SetMaxIdleConns(5)
   db.SetConnMaxLifetime(5 * time.Minute)
   ```

### Cache Optimization

1. **Hit Rate Improvement**
   - Analyze cache miss patterns
   - Optimize cache key strategies
   - Implement cache warming

2. **Memory Management**
   - Configure appropriate TTL values
   - Implement cache size limits
   - Use compression for large values

### Service Optimization

1. **gRPC Performance**
   - Connection pooling
   - Compression configuration
   - Keepalive settings

2. **HTTP Performance**
   - Connection reuse
   - Compression middleware
   - Response caching

## Troubleshooting

### Common Issues

#### High Latency
1. Check database query performance
2. Analyze network latency
3. Review service resource usage
4. Check for lock contention

#### Low Throughput
1. Verify connection pool settings
2. Check for CPU/memory bottlenecks
3. Analyze garbage collection impact
4. Review concurrency settings

#### Memory Issues
1. Check for memory leaks
2. Analyze allocation patterns
3. Review garbage collection settings
4. Optimize data structures

#### Inconsistent Results
1. Ensure stable test environment
2. Check for background processes
3. Verify test data consistency
4. Review test isolation

### Debugging Tools

```bash
# Service logs during benchmarks
docker-compose logs -f [service-name]

# System resource monitoring
htop
iostat -x 1
free -h

# Network analysis
netstat -i
ss -tuln

# Database performance
pg_stat_activity
pg_stat_statements
```

## Best Practices

### Test Environment

1. **Isolation**: Run benchmarks in dedicated environment
2. **Consistency**: Use consistent hardware and configuration
3. **Baseline**: Establish performance baselines
4. **Monitoring**: Monitor system resources during tests

### Test Design

1. **Realistic Data**: Use production-like test data
2. **Scenarios**: Test realistic user scenarios
3. **Gradual Load**: Implement proper ramp-up
4. **Error Handling**: Test error conditions

### Result Analysis

1. **Multiple Runs**: Run tests multiple times for consistency
2. **Statistical Analysis**: Use proper statistical methods
3. **Trend Analysis**: Monitor performance over time
4. **Root Cause**: Investigate performance issues thoroughly

### Continuous Improvement

1. **Regular Testing**: Run benchmarks regularly
2. **Automated Alerts**: Set up performance regression alerts
3. **Performance Reviews**: Regular performance review meetings
4. **Optimization Cycles**: Continuous performance optimization

---

For more information on specific benchmark implementations, see the benchmark test files in each service's `benchmark/` directory.
