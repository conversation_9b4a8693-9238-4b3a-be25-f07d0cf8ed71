# GitLab Multi-Repository Benchmark Solution

## Overview

I've successfully modified your benchmark script to work with GitLab-hosted microservice repositories. The solution provides automatic Git operations (clone/pull) before running benchmarks, making it perfect for multi-repository architectures where services are stored in separate GitLab repositories.

## What Was Modified

### 1. Enhanced Benchmark Script (`scripts/run-benchmarks.sh`)
- **Git Integration**: Added automatic clone/pull functionality
- **Flexible Configuration**: Support for both local paths and Git URLs
- **Authentication Support**: HTTPS with tokens and SSH key authentication
- **Branch Selection**: Ability to test specific branches
- **Error Handling**: Robust error handling for Git operations

### 2. Configuration System
- **GitLab-Specific Template**: `benchmark-config-gitlab.env.example`
- **Example Configuration**: `example-gitlab-config.env`
- **Flexible Setup**: Support for public/private repositories

### 3. Setup Tools
- **Automated Setup**: `setup-gitlab-benchmarks.sh` for guided configuration
- **Repository Testing**: Automatic validation of repository access
- **Authentication Helper**: Guided setup for different auth methods

## Key Features

### ✅ Multi-Repository Support
- Automatically clone repositories from GitLab
- Update existing repositories with latest changes
- Support for different branches per test run

### ✅ Flexible Authentication
- **Public Repositories**: No authentication required
- **Private Repositories**: Personal Access Token support
- **SSH Authentication**: SSH key-based access
- **CI/CD Integration**: GitLab CI variables support

### ✅ Robust Configuration
- Environment variable overrides
- Configuration file support
- Default fallbacks for missing configurations
- Validation and error reporting

### ✅ GitLab CI/CD Ready
- Pipeline integration examples
- Artifact collection
- Branch-specific testing
- Automated credential handling

## Quick Start Guide

### Option 1: Automated Setup (Recommended)
```bash
# Run the GitLab setup wizard
./scripts/setup-gitlab-benchmarks.sh

# Follow the prompts to configure your repositories
# The script will test access and create the configuration file

# Run benchmarks
./scripts/run-benchmarks.sh
```

### Option 2: Manual Configuration
```bash
# Copy the GitLab template
cp scripts/benchmark-config-gitlab.env.example scripts/benchmark-config.env

# Edit the configuration file
# Set your GitLab group, repository URLs, and authentication

# Run benchmarks
./scripts/run-benchmarks.sh
```

### Option 3: Environment Variables
```bash
# Set environment variables
export GIT_AUTO_PULL="true"
export GIT_BASE_DIR="/tmp/benchmark-repos"
export AUTH_SERVICE_GIT_URL="https://gitlab.com/your-group/auth-service.git"
# ... set other service URLs

# Run benchmarks
./scripts/run-benchmarks.sh
```

## Configuration Examples

### Public Repositories
```bash
GIT_AUTO_PULL="true"
GIT_BASE_DIR="/tmp/benchmark-repos"
GIT_BRANCH="main"
AUTH_SERVICE_GIT_URL="https://gitlab.com/your-group/auth-service.git"
```

### Private Repositories with Token
```bash
GIT_AUTO_PULL="true"
GIT_BASE_DIR="/tmp/benchmark-repos"
GIT_BRANCH="main"
GIT_USERNAME="your-username"
GIT_TOKEN="glpat-xxxxxxxxxxxxxxxxxxxx"
AUTH_SERVICE_GIT_URL="https://gitlab.com/your-group/auth-service.git"
```

### SSH Authentication
```bash
GIT_AUTO_PULL="true"
GIT_BASE_DIR="/tmp/benchmark-repos"
GIT_BRANCH="main"
AUTH_SERVICE_GIT_URL="**************:your-group/auth-service.git"
```

## Workflow

When you run the benchmark script with GitLab integration:

1. **Configuration Loading**: Loads GitLab URLs and authentication
2. **Repository Updates**: For each service:
   - Clones repository if it doesn't exist locally
   - Pulls latest changes if repository exists
   - Switches to specified branch
3. **Benchmark Execution**: Runs benchmarks on updated code
4. **Report Generation**: Creates performance reports

## Files Created/Modified

### Modified Files
- `scripts/run-benchmarks.sh` - Enhanced with Git functionality

### New Configuration Files
- `scripts/benchmark-config-gitlab.env.example` - GitLab template
- `scripts/example-gitlab-config.env` - Working example
- `scripts/benchmark-config.env.example` - Updated general template

### New Setup Tools
- `scripts/setup-gitlab-benchmarks.sh` - Automated GitLab setup
- `scripts/setup-benchmark-config.sh` - General setup tool

### New Documentation
- `scripts/README-gitlab-benchmarks.md` - GitLab-specific guide
- `scripts/README-benchmarks.md` - Updated general guide
- `GITLAB_BENCHMARK_SOLUTION.md` - This summary

## Usage Scenarios

### Development Testing
```bash
# Test current branch
./scripts/run-benchmarks.sh quick

# Test specific branch
GIT_BRANCH="feature/performance-fix" ./scripts/run-benchmarks.sh
```

### CI/CD Integration
```yaml
# .gitlab-ci.yml
benchmark:
  script:
    - ./scripts/run-benchmarks.sh quick
  variables:
    GIT_AUTO_PULL: "true"
    GIT_BRANCH: "$CI_COMMIT_REF_NAME"
```

### Release Testing
```bash
# Test release branch with full benchmarks
GIT_BRANCH="release/v1.2.0" ./scripts/run-benchmarks.sh load
```

## Benefits

1. **No Local Setup Required**: Repositories are automatically cloned
2. **Always Up-to-Date**: Latest code is pulled before each benchmark run
3. **Branch Flexibility**: Test any branch without manual checkout
4. **CI/CD Ready**: Perfect for automated testing pipelines
5. **Multi-Environment**: Same script works locally and in CI/CD
6. **Secure**: Supports multiple authentication methods
7. **Robust**: Comprehensive error handling and validation

## Next Steps

1. **Configure Your Setup**: Use `./scripts/setup-gitlab-benchmarks.sh`
2. **Test Repository Access**: Verify all repositories are accessible
3. **Run Initial Benchmarks**: Test with `./scripts/run-benchmarks.sh quick`
4. **Integrate with CI/CD**: Add to your GitLab pipeline
5. **Customize as Needed**: Adjust configuration for your specific needs

## Support

The solution includes comprehensive documentation and examples:
- Detailed README files for different scenarios
- Configuration templates with examples
- Troubleshooting guides
- CI/CD integration examples

Your benchmark script is now ready to work seamlessly with GitLab-hosted microservice repositories!
