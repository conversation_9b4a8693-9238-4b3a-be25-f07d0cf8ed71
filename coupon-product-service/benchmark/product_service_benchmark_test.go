package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"

	productv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

var (
	productGrpcSuite  *benchmark.GRPCBenchmarkSuite
	productDbSuite    *benchmark.DatabaseBenchmarkSuite
	productCacheSuite *benchmark.CacheBenchmarkSuite
	productTestDB     *database.DB
	productTestRedis  *redis.Client
	productGenerator  *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupProductBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup product benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	cleanupProduct()
	os.Exit(code)
}

func setupProductBenchmarkEnvironment() error {
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("product-service-benchmark")

	productTestDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	productTestRedis = redis.NewClient(&cfg.Redis, logger, appMetrics)

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50055", // Product service gRPC port
		ServiceName:    "product-service",
		Timeout:        5 * time.Second,
	}

	productGrpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "product-service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	productDbSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, productTestDB.DB)

	productCacheSuite = benchmark.NewCacheBenchmarkSuite("product-service", productTestRedis)
	productGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanupProduct() {
	if productGrpcSuite != nil {
		productGrpcSuite.Close()
	}
	if productTestDB != nil {
		sqlDB, _ := productTestDB.DB.DB()
		sqlDB.Close()
	}
	if productTestRedis != nil {
		productTestRedis.Close()
	}
}

// gRPC Endpoint Benchmarks
func BenchmarkProductService_gRPC_GetProduct(b *testing.B) {
	productGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := productv1.NewProductServiceClient(conn)
		ctx = addProductAuthMetadata(ctx)

		req := &productv1.GetProductRequest{
			ProductId: uint64(productGenerator.GenerateProductID()),
		}

		_, err := client.GetProduct(ctx, req)
		return err
	})
}

func BenchmarkProductService_gRPC_ListProducts(b *testing.B) {
	productGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := productv1.NewProductServiceClient(conn)
		ctx = addProductAuthMetadata(ctx)

		req := &productv1.ListProductsRequest{
			// Add pagination parameters as needed
		}

		_, err := client.ListProducts(ctx, req)
		return err
	})
}

func BenchmarkProductService_gRPC_UpdateProduct(b *testing.B) {
	productGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := productv1.NewProductServiceClient(conn)
		ctx = addProductAuthMetadata(ctx)

		req := &productv1.UpdateProductRequest{
			ProductId: uint64(productGenerator.GenerateProductID()),
			Name:      fmt.Sprintf("Updated Product_%d", productGenerator.GenerateProductID()),
			Price:     productGenerator.GeneratePrice(),
		}

		_, err := client.UpdateProduct(ctx, req)
		return err
	})
}

func BenchmarkProductService_gRPC_ListCategories(b *testing.B) {
	productGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := productv1.NewProductServiceClient(conn)
		ctx = addProductAuthMetadata(ctx)

		req := &productv1.ListCategoriesRequest{
			// Add any filters as needed
		}

		_, err := client.ListCategories(ctx, req)
		return err
	})
}

func BenchmarkProductService_gRPC_HealthCheck(b *testing.B) {
	productGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := productv1.NewProductServiceClient(conn)
		ctx = addProductAuthMetadata(ctx)

		// Need to import common proto for HealthCheckRequest
		// For now, let's use a simple approach
		_, err := client.GetProduct(ctx, &productv1.GetProductRequest{
			ProductId: uint64(productGenerator.GenerateProductID()),
		})
		return err
	})
}

// Database Operation Benchmarks
func BenchmarkProductService_Database_ProductQueries(b *testing.B) {
	queryBenchmarks := benchmark.NewDatabaseQueryBenchmarks(productDbSuite)
	queryBenchmarks.BenchmarkProductQueries(b)
}

func BenchmarkProductService_Database_GetProductByID(b *testing.B) {
	productDbSuite.BenchmarkQuery(b, "get_product_by_id", func(ctx context.Context, db *gorm.DB) error {
		productID := productGenerator.GenerateProductID()
		var product struct {
			ID          int     `gorm:"column:id"`
			Name        string  `gorm:"column:name"`
			Price       float64 `gorm:"column:price"`
			CategoryID  int     `gorm:"column:category_id"`
			Stock       int     `gorm:"column:stock"`
			Description string  `gorm:"column:description"`
		}
		return db.Table("products").Where("id = ?", productID).First(&product).Error
	})
}

func BenchmarkProductService_Database_SearchProducts(b *testing.B) {
	productDbSuite.BenchmarkQuery(b, "search_products", func(ctx context.Context, db *gorm.DB) error {
		searchTerm := fmt.Sprintf("Product_%d", productGenerator.GenerateProductID()%100)
		var products []struct {
			ID    int     `gorm:"column:id"`
			Name  string  `gorm:"column:name"`
			Price float64 `gorm:"column:price"`
		}
		return db.Table("products").
			Where("name ILIKE ? OR description ILIKE ?", "%"+searchTerm+"%", "%"+searchTerm+"%").
			Limit(20).Find(&products).Error
	})
}

func BenchmarkProductService_Database_GetProductsByCategory(b *testing.B) {
	productDbSuite.BenchmarkQuery(b, "get_products_by_category", func(ctx context.Context, db *gorm.DB) error {
		categoryID := productGenerator.GenerateCategoryID()
		var products []struct {
			ID    int     `gorm:"column:id"`
			Name  string  `gorm:"column:name"`
			Price float64 `gorm:"column:price"`
		}
		return db.Table("products").
			Where("category_id = ? AND stock > 0", categoryID).
			Limit(20).Find(&products).Error
	})
}

func BenchmarkProductService_Database_UpdateStock(b *testing.B) {
	productDbSuite.BenchmarkTransaction(b, "update_stock", func(ctx context.Context, tx *gorm.DB) error {
		productID := productGenerator.GenerateProductID()
		quantity := productGenerator.GenerateQuantity()

		// Check current stock
		var currentStock int
		if err := tx.Table("products").
			Select("stock").
			Where("id = ?", productID).
			Scan(&currentStock).Error; err != nil {
			return err
		}

		// Update stock
		return tx.Table("products").
			Where("id = ?", productID).
			Update("stock", currentStock+quantity).Error
	})
}

func BenchmarkProductService_Database_ListCategories(b *testing.B) {
	productDbSuite.BenchmarkQuery(b, "list_categories", func(ctx context.Context, db *gorm.DB) error {
		var categories []struct {
			ID   int    `gorm:"column:id"`
			Name string `gorm:"column:name"`
		}
		return db.Table("categories").Find(&categories).Error
	})
}

// Redis Cache Benchmarks
func BenchmarkProductService_Cache_GetProduct(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "product-service",
		TestName:       "cache_get_product",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		productID := productGenerator.GenerateProductID()
		cacheKey := fmt.Sprintf("product:%d", productID)

		// Try to get from cache first
		_, err := productTestRedis.Get(ctx, cacheKey)
		if err == nil {
			return nil // Cache hit
		}

		// Cache miss - simulate database lookup and cache set
		productData := productGenerator.GenerateProductData()
		return productTestRedis.Set(ctx, cacheKey, productData, 30*time.Minute)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkProductService_Cache_SearchResults(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "product-service",
		TestName:       "cache_search_results",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		searchTerm := fmt.Sprintf("search_%d", productGenerator.GenerateProductID()%100)
		cacheKey := fmt.Sprintf("search:%s:page:1", searchTerm)

		// Try to get from cache
		_, err := productTestRedis.Get(ctx, cacheKey)
		if err == nil {
			return nil // Cache hit
		}

		// Cache miss - simulate search and cache results
		searchResults := []map[string]interface{}{
			productGenerator.GenerateProductData(),
			productGenerator.GenerateProductData(),
		}

		return productTestRedis.Set(ctx, cacheKey, searchResults, 10*time.Minute)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkProductService_Cache_CategoryProducts(b *testing.B) {
	productCacheSuite.BenchmarkCacheGet(b, func(ctx context.Context, key string) error {
		categoryID := productGenerator.GenerateCategoryID()
		cacheKey := fmt.Sprintf("category:%d:products", categoryID)
		_, err := productTestRedis.Get(ctx, cacheKey)
		return err
	})
}

// Concurrent Load Tests
func BenchmarkProductService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "product-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 15,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "get_product",
				Weight: 40,
				Execute: func(ctx context.Context) error {
					ctx = addProductAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50054",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := productv1.NewProductServiceClient(conn)
					req := &productv1.GetProductRequest{
						ProductId: uint64(productGenerator.GenerateProductID()),
					}
					_, err = client.GetProduct(ctx, req)
					return err
				},
			},
			{
				Name:   "list_products",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addProductAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50054",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := productv1.NewProductServiceClient(conn)
					req := &productv1.ListProductsRequest{}
					_, err = client.ListProducts(ctx, req)
					return err
				},
			},
			{
				Name:   "list_categories",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addProductAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50054",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := productv1.NewProductServiceClient(conn)
					req := &productv1.ListCategoriesRequest{}
					_, err = client.ListCategories(ctx, req)
					return err
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

// Helper functions
func addProductAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "product-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}

// Helper functions for generating test data
func generateProductUserID() int {
	return productGenerator.Rand().Intn(1000) + 1
}
