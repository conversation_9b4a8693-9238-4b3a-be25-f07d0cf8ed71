package metrics

import (
	"time"
)

// BusinessMetrics provides service-specific business metrics helpers
type BusinessMetrics struct {
	metrics     *Metrics
	serviceName string
}

// NewBusinessMetrics creates a new business metrics helper
func NewBusinessMetrics(metrics *Metrics, serviceName string) *BusinessMetrics {
	return &BusinessMetrics{
		metrics:     metrics,
		serviceName: serviceName,
	}
}

// User Service Business Metrics
func (bm *BusinessMetrics) RecordUserRegistration(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "user_registration", status, duration)
}

func (bm *BusinessMetrics) RecordUserLogin(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "user_login", status, duration)
}

func (bm *BusinessMetrics) RecordUserProfileUpdate(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "user_profile_update", status, duration)
}

// Voucher Service Business Metrics
func (bm *BusinessMetrics) RecordVoucherCreation(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "voucher_creation", status, duration)
}

func (bm *BusinessMetrics) RecordVoucherRedemption(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "voucher_redemption", status, duration)
}

func (bm *BusinessMetrics) RecordVoucherValidation(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "voucher_validation", status, duration)
}

func (bm *BusinessMetrics) RecordVoucherExpiry(count int) {
	for i := 0; i < count; i++ {
		bm.metrics.RecordBusinessOperation(bm.serviceName, "voucher_expiry", "expired", 0)
	}
}

// Product Service Business Metrics
func (bm *BusinessMetrics) RecordProductSearch(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "product_search", status, duration)
}

func (bm *BusinessMetrics) RecordProductView(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "product_view", status, duration)
}

func (bm *BusinessMetrics) RecordCategoryList(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "category_list", status, duration)
}

// Order Service Business Metrics
func (bm *BusinessMetrics) RecordOrderCreation(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "order_creation", status, duration)
}

func (bm *BusinessMetrics) RecordOrderProcessing(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "order_processing", status, duration)
}

func (bm *BusinessMetrics) RecordOrderCancellation(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "order_cancellation", status, duration)
}

// Notification Service Business Metrics
func (bm *BusinessMetrics) RecordNotificationSent(notificationType, status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "notification_"+notificationType, status, duration)
}

func (bm *BusinessMetrics) RecordTemplateRendering(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "template_rendering", status, duration)
}

// Auth Service Business Metrics
func (bm *BusinessMetrics) RecordTokenGeneration(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "token_generation", status, duration)
}

func (bm *BusinessMetrics) RecordTokenValidation(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "token_validation", status, duration)
}

func (bm *BusinessMetrics) RecordServiceAuthentication(status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "service_authentication", status, duration)
}

// API Gateway Business Metrics
func (bm *BusinessMetrics) RecordAPIRequest(endpoint, method, status string, duration time.Duration) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "api_request_"+endpoint+"_"+method, status, duration)
}

func (bm *BusinessMetrics) RecordRateLimitHit(endpoint string) {
	bm.metrics.RecordBusinessOperation(bm.serviceName, "rate_limit_hit", "blocked", 0)
}

// Generic Business Error Recording
func (bm *BusinessMetrics) RecordError(operation, errorType string) {
	bm.metrics.RecordBusinessError(bm.serviceName, operation, errorType)
}

// Performance Metrics
func (bm *BusinessMetrics) RecordSlowQuery(operation string, duration time.Duration) {
	if duration > 1*time.Second {
		bm.metrics.RecordBusinessError(bm.serviceName, operation, "slow_query")
	}
}

func (bm *BusinessMetrics) RecordHighMemoryUsage(memoryMB float64) {
	if memoryMB > 500 { // Alert if memory usage > 500MB
		bm.metrics.RecordBusinessError(bm.serviceName, "memory_usage", "high_memory")
	}
}
