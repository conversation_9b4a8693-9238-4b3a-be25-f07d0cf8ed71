package benchmark

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"testing"
	"time"

	"gorm.io/gorm"
)

// DatabaseBenchmarkConfig holds configuration for database benchmarks
type DatabaseBenchmarkConfig struct {
	ServiceName    string
	DatabaseType   string // "postgres", "mysql", etc.
	ConnectionPool int
	QueryTimeout   time.Duration
}

// DatabaseBenchmarkSuite provides utilities for benchmarking database operations
type DatabaseBenchmarkSuite struct {
	config *DatabaseBenchmarkConfig
	db     *gorm.DB
	sqlDB  *sql.DB
	mu     sync.RWMutex
}

// NewDatabaseBenchmarkSuite creates a new database benchmark suite
func NewDatabaseBenchmarkSuite(config *DatabaseBenchmarkConfig, db *gorm.DB) *DatabaseBenchmarkSuite {
	sqlDB, _ := db.DB()
	return &DatabaseBenchmarkSuite{
		config: config,
		db:     db,
		sqlDB:  sqlDB,
	}
}

// BenchmarkQuery benchmarks a database query operation
func (dbs *DatabaseBenchmarkSuite) BenchmarkQuery(
	b *testing.B,
	queryName string,
	queryFunc func(ctx context.Context, db *gorm.DB) error,
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    dbs.config.ServiceName,
		TestName:       fmt.Sprintf("db_query_%s", queryName),
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, dbs.config.QueryTimeout)
		defer cancel()
		return queryFunc(ctx, dbs.db.WithContext(ctx))
	}

	framework.RunBenchmark(b, operation)
}

// BenchmarkTransaction benchmarks a database transaction
func (dbs *DatabaseBenchmarkSuite) BenchmarkTransaction(
	b *testing.B,
	transactionName string,
	transactionFunc func(ctx context.Context, tx *gorm.DB) error,
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    dbs.config.ServiceName,
		TestName:       fmt.Sprintf("db_transaction_%s", transactionName),
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, dbs.config.QueryTimeout)
		defer cancel()
		
		return dbs.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			return transactionFunc(ctx, tx)
		})
	}

	framework.RunBenchmark(b, operation)
}

// BenchmarkConnectionPool benchmarks database connection pool performance
func (dbs *DatabaseBenchmarkSuite) BenchmarkConnectionPool(b *testing.B, concurrency int) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    dbs.config.ServiceName,
		TestName:       "db_connection_pool",
		Concurrency:    concurrency,
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, dbs.config.QueryTimeout)
		defer cancel()
		
		// Simple ping to test connection acquisition
		return dbs.db.WithContext(ctx).Exec("SELECT 1").Error
	}

	framework.RunBenchmark(b, operation)
}

// BenchmarkBulkInsert benchmarks bulk insert operations
func (dbs *DatabaseBenchmarkSuite) BenchmarkBulkInsert(
	b *testing.B,
	tableName string,
	batchSize int,
	generateRecord func() interface{},
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    dbs.config.ServiceName,
		TestName:       fmt.Sprintf("db_bulk_insert_%s", tableName),
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, dbs.config.QueryTimeout*5) // Longer timeout for bulk ops
		defer cancel()
		
		records := make([]interface{}, batchSize)
		for i := 0; i < batchSize; i++ {
			records[i] = generateRecord()
		}
		
		return dbs.db.WithContext(ctx).CreateInBatches(records, batchSize).Error
	}

	framework.RunBenchmark(b, operation)
}

// CacheBenchmarkSuite provides utilities for benchmarking cache operations
type CacheBenchmarkSuite struct {
	serviceName string
	cacheClient interface{} // Redis client or other cache implementation
}

// NewCacheBenchmarkSuite creates a new cache benchmark suite
func NewCacheBenchmarkSuite(serviceName string, cacheClient interface{}) *CacheBenchmarkSuite {
	return &CacheBenchmarkSuite{
		serviceName: serviceName,
		cacheClient: cacheClient,
	}
}

// BenchmarkCacheGet benchmarks cache get operations
func (cbs *CacheBenchmarkSuite) BenchmarkCacheGet(
	b *testing.B,
	getFunc func(ctx context.Context, key string) error,
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    cbs.serviceName,
		TestName:       "cache_get",
		MetricsEnabled: true,
	})

	generator := NewTestDataGenerator()
	
	operation := func(ctx context.Context) error {
		key := fmt.Sprintf("test_key_%d", generator.rand.Intn(1000))
		return getFunc(ctx, key)
	}

	framework.RunBenchmark(b, operation)
}

// BenchmarkCacheSet benchmarks cache set operations
func (cbs *CacheBenchmarkSuite) BenchmarkCacheSet(
	b *testing.B,
	setFunc func(ctx context.Context, key string, value interface{}) error,
) {
	framework := NewBenchmarkFramework(&BenchmarkConfig{
		ServiceName:    cbs.serviceName,
		TestName:       "cache_set",
		MetricsEnabled: true,
	})

	generator := NewTestDataGenerator()
	
	operation := func(ctx context.Context) error {
		key := fmt.Sprintf("test_key_%d", generator.rand.Intn(1000))
		value := generator.GenerateUserData()
		return setFunc(ctx, key, value)
	}

	framework.RunBenchmark(b, operation)
}

// DatabaseQueryBenchmarks provides common database query benchmarks
type DatabaseQueryBenchmarks struct {
	suite *DatabaseBenchmarkSuite
}

// NewDatabaseQueryBenchmarks creates common database query benchmarks
func NewDatabaseQueryBenchmarks(suite *DatabaseBenchmarkSuite) *DatabaseQueryBenchmarks {
	return &DatabaseQueryBenchmarks{suite: suite}
}

// BenchmarkUserQueries runs user-related query benchmarks
func (dqb *DatabaseQueryBenchmarks) BenchmarkUserQueries(b *testing.B) {
	generator := NewTestDataGenerator()
	
	b.Run("GetUserByID", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "get_user_by_id", func(ctx context.Context, db *gorm.DB) error {
			userID := generator.rand.Intn(1000) + 1
			var user struct {
				ID       int    `gorm:"column:id"`
				Username string `gorm:"column:username"`
				Email    string `gorm:"column:email"`
			}
			return db.Table("users").Where("id = ?", userID).First(&user).Error
		})
	})
	
	b.Run("GetUserByEmail", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "get_user_by_email", func(ctx context.Context, db *gorm.DB) error {
			email := fmt.Sprintf("<EMAIL>", generator.rand.Intn(1000))
			var user struct {
				ID       int    `gorm:"column:id"`
				Username string `gorm:"column:username"`
				Email    string `gorm:"column:email"`
			}
			return db.Table("users").Where("email = ?", email).First(&user).Error
		})
	})
	
	b.Run("ListUsers", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "list_users", func(ctx context.Context, db *gorm.DB) error {
			var users []struct {
				ID       int    `gorm:"column:id"`
				Username string `gorm:"column:username"`
				Email    string `gorm:"column:email"`
			}
			return db.Table("users").Limit(20).Find(&users).Error
		})
	})
}

// BenchmarkVoucherQueries runs voucher-related query benchmarks
func (dqb *DatabaseQueryBenchmarks) BenchmarkVoucherQueries(b *testing.B) {
	generator := NewTestDataGenerator()
	
	b.Run("GetVoucherByCode", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "get_voucher_by_code", func(ctx context.Context, db *gorm.DB) error {
			code := fmt.Sprintf("VOUCHER_%d", generator.rand.Intn(10000))
			var voucher struct {
				ID       int     `gorm:"column:id"`
				Code     string  `gorm:"column:code"`
				Discount float64 `gorm:"column:discount"`
			}
			return db.Table("vouchers").Where("code = ?", code).First(&voucher).Error
		})
	})
	
	b.Run("ListActiveVouchers", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "list_active_vouchers", func(ctx context.Context, db *gorm.DB) error {
			var vouchers []struct {
				ID       int     `gorm:"column:id"`
				Code     string  `gorm:"column:code"`
				Discount float64 `gorm:"column:discount"`
			}
			return db.Table("vouchers").Where("expires_at > NOW() AND is_active = true").Limit(50).Find(&vouchers).Error
		})
	})
}

// BenchmarkProductQueries runs product-related query benchmarks
func (dqb *DatabaseQueryBenchmarks) BenchmarkProductQueries(b *testing.B) {
	generator := NewTestDataGenerator()
	
	b.Run("GetProductByID", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "get_product_by_id", func(ctx context.Context, db *gorm.DB) error {
			productID := generator.rand.Intn(1000) + 1
			var product struct {
				ID    int     `gorm:"column:id"`
				Name  string  `gorm:"column:name"`
				Price float64 `gorm:"column:price"`
			}
			return db.Table("products").Where("id = ?", productID).First(&product).Error
		})
	})
	
	b.Run("SearchProducts", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "search_products", func(ctx context.Context, db *gorm.DB) error {
			searchTerm := fmt.Sprintf("Product_%d", generator.rand.Intn(100))
			var products []struct {
				ID    int     `gorm:"column:id"`
				Name  string  `gorm:"column:name"`
				Price float64 `gorm:"column:price"`
			}
			return db.Table("products").Where("name ILIKE ?", "%"+searchTerm+"%").Limit(20).Find(&products).Error
		})
	})
	
	b.Run("GetProductsByCategory", func(b *testing.B) {
		dqb.suite.BenchmarkQuery(b, "get_products_by_category", func(ctx context.Context, db *gorm.DB) error {
			categoryID := generator.rand.Intn(10) + 1
			var products []struct {
				ID    int     `gorm:"column:id"`
				Name  string  `gorm:"column:name"`
				Price float64 `gorm:"column:price"`
			}
			return db.Table("products").Where("category_id = ?", categoryID).Limit(20).Find(&products).Error
		})
	})
}
