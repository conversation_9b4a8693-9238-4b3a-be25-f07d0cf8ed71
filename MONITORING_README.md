# Coupon System - Monitoring and Observability

This document provides comprehensive guidance for setting up and using the monitoring and observability infrastructure for the Coupon microservice system.

## Overview

The monitoring stack includes:
- **Prometheus** - Metrics collection and storage
- **Grafana** - Visualization and dashboards
- **AlertManager** - Alert handling and notifications
- **Node Exporter** - System metrics
- **cAdvisor** - Container metrics
- **Benchmark Framework** - Performance testing and analysis

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 4GB RAM available for monitoring stack
- Ports 3000, 8080, 9090, 9093, 9100 available

### Deployment

1. **Deploy the monitoring stack:**
   ```bash
   chmod +x scripts/deploy-monitoring.sh
   ./scripts/deploy-monitoring.sh
   ```

2. **Access the services:**
   - Grafana: http://localhost:3000 (admin/admin123)
   - Prometheus: http://localhost:9090
   - AlertManager: http://localhost:9093

3. **Run benchmarks:**
   ```bash
   ./scripts/run-benchmarks.sh
   ```

## Architecture

### Monitoring Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Microservices │───▶│   Prometheus    │───▶│     Grafana     │
│                 │    │                 │    │                 │
│ - API Gateway   │    │ - Metrics Store │    │ - Dashboards    │
│ - Auth Service  │    │ - Alerting      │    │ - Visualization │
│ - User Service  │    │ - Scraping      │    │ - Analysis      │
│ - Voucher Svc   │    └─────────────────┘    └─────────────────┘
│ - Product Svc   │             │                       │
│ - Order Service │             ▼                       │
│ - Notification  │    ┌─────────────────┐             │
└─────────────────┘    │  AlertManager   │◀────────────┘
                       │                 │
                       │ - Notifications │
                       │ - Routing       │
                       │ - Silencing     │
                       └─────────────────┘
```

### Metrics Collection

Each service exposes metrics on port 2112 at `/metrics` endpoint:

- **HTTP Metrics**: Request rate, duration, status codes
- **gRPC Metrics**: Request rate, duration, status codes
- **Database Metrics**: Query performance, connection pool
- **Cache Metrics**: Hit rate, latency, size
- **Business Metrics**: User registrations, voucher redemptions, orders
- **System Metrics**: Memory, CPU, connections

## Dashboards

### Available Dashboards

1. **System Overview** (`/d/coupon-overview`)
   - Service health and performance overview
   - Request rates and response times
   - Error rates across services

2. **Business Metrics** (`/d/coupon-business`)
   - User registration and login rates
   - Voucher creation and redemption
   - Order processing metrics

3. **API Gateway Details** (`/d/api-gateway-detailed`)
   - HTTP endpoint performance
   - Authentication metrics
   - Rate limiting statistics

4. **Benchmark Performance** (`/d/benchmark-performance`)
   - Benchmark test results
   - Performance trends over time
   - Resource utilization during tests

5. **Performance Regression** (`/d/performance-regression`)
   - Performance comparison with historical data
   - Regression detection and alerts

### Dashboard Features

- **Real-time monitoring** with 15-second refresh intervals
- **Historical analysis** with configurable time ranges
- **Interactive filtering** by service, endpoint, and status
- **Alerting integration** with visual indicators
- **Export capabilities** for reports and analysis

## Alerting

### Alert Categories

#### Service Health Alerts
- Service down detection
- High error rates (>5% warning, >10% critical)
- High response times (>1s warning, >3s critical)

#### Database Alerts
- High connection usage (>80% warning, >95% critical)
- Slow queries (>1s warning)
- Database errors

#### Business Logic Alerts
- High business operation failures
- Voucher redemption issues
- User registration problems
- Order processing failures

#### Security Alerts
- High authentication failure rates
- Suspicious request patterns
- Rate limiting violations

#### Performance Alerts
- Memory usage (>1GB warning, >2GB critical)
- CPU usage (>80% warning, >95% critical)
- Cache performance degradation

### Alert Configuration

Alerts are configured in `monitoring/prometheus/rules/`:
- `service_alerts.yml` - Service health and performance
- `security_alerts.yml` - Security and authentication

### Notification Channels

Configure notification channels in `monitoring/alertmanager/alertmanager.yml`:
- Email notifications
- Webhook integrations
- Slack/Teams integration (configure as needed)

## Benchmark Testing

### Framework Features

The benchmark framework provides:
- **gRPC endpoint testing** with realistic payloads
- **Database performance testing** with connection pooling
- **Cache performance testing** with Redis operations
- **Load testing scenarios** with configurable concurrency
- **Performance regression detection**
- **Metrics integration** with Prometheus

### Running Benchmarks

#### Individual Service Benchmarks
```bash
# User service benchmarks
cd coupon-user-service
go test -bench=. -benchmem ./benchmark/...

# Voucher service benchmarks
cd coupon-voucher-service
go test -bench=. -benchmem ./benchmark/...

# API Gateway benchmarks
cd coupon-api-gateway
go test -bench=. -benchmem ./benchmark/...
```

#### All Services Benchmark
```bash
./scripts/run-benchmarks.sh
```

#### Continuous Benchmarking
```bash
# Run benchmarks every hour
crontab -e
# Add: 0 * * * * /path/to/coupon-microservice/scripts/run-benchmarks.sh
```

### Benchmark Types

1. **gRPC Endpoint Benchmarks**
   - Unary call performance
   - Streaming call performance
   - Concurrent request handling

2. **Database Benchmarks**
   - Query performance
   - Transaction performance
   - Connection pool efficiency
   - Bulk operations

3. **Cache Benchmarks**
   - Get/Set operations
   - Hit rate optimization
   - Latency measurement

4. **Load Testing**
   - Mixed workload scenarios
   - Stress testing
   - Ramp-up testing

### Performance Baselines

Recommended performance targets:
- **HTTP Response Time**: <500ms (95th percentile)
- **gRPC Response Time**: <200ms (95th percentile)
- **Database Queries**: <100ms (95th percentile)
- **Cache Operations**: <10ms (95th percentile)
- **Error Rate**: <1%
- **Availability**: >99.5%

## Troubleshooting

### Common Issues

#### Monitoring Stack Won't Start
```bash
# Check Docker daemon
sudo systemctl status docker

# Check port conflicts
netstat -tulpn | grep -E ':(3000|8080|9090|9093|9100)'

# Check logs
docker-compose -f docker-compose.monitoring.yml logs
```

#### Services Not Appearing in Prometheus
1. Verify service is running and exposing metrics on port 2112
2. Check Prometheus configuration in `monitoring/prometheus/prometheus.yml`
3. Verify network connectivity: `docker network ls`
4. Check Prometheus targets: http://localhost:9090/targets

#### Grafana Dashboards Not Loading
1. Verify Prometheus datasource: http://localhost:3000/datasources
2. Check dashboard JSON syntax
3. Verify metrics are being collected in Prometheus

#### Benchmark Tests Failing
1. Ensure services are running and accessible
2. Check authentication credentials in test setup
3. Verify database and Redis connections
4. Check network connectivity between test and services

### Performance Optimization

#### Prometheus Optimization
- Adjust scrape intervals based on needs
- Configure retention policies
- Use recording rules for complex queries

#### Grafana Optimization
- Limit dashboard refresh rates
- Use appropriate time ranges
- Optimize query performance

#### Service Optimization
- Monitor memory usage and optimize if needed
- Tune database connection pools
- Optimize cache configurations
- Review and optimize slow queries

## Maintenance

### Regular Tasks

#### Daily
- Review alert notifications
- Check service health dashboards
- Monitor error rates and performance

#### Weekly
- Review benchmark results for regressions
- Analyze performance trends
- Update alert thresholds if needed

#### Monthly
- Clean up old metrics data
- Review and update dashboards
- Performance capacity planning

### Backup and Recovery

#### Prometheus Data
```bash
# Backup Prometheus data
docker run --rm -v prometheus-data:/data -v $(pwd):/backup alpine tar czf /backup/prometheus-backup.tar.gz /data

# Restore Prometheus data
docker run --rm -v prometheus-data:/data -v $(pwd):/backup alpine tar xzf /backup/prometheus-backup.tar.gz -C /
```

#### Grafana Configuration
```bash
# Backup Grafana data
docker run --rm -v grafana-data:/data -v $(pwd):/backup alpine tar czf /backup/grafana-backup.tar.gz /data

# Restore Grafana data
docker run --rm -v grafana-data:/data -v $(pwd):/backup alpine tar xzf /backup/grafana-backup.tar.gz -C /
```

## Security Considerations

- Change default Grafana admin password
- Configure proper authentication for production
- Use HTTPS for external access
- Implement proper network segmentation
- Regular security updates for monitoring components

## Support and Contributing

For issues and contributions:
1. Check existing documentation
2. Review logs and metrics
3. Create detailed issue reports
4. Follow contribution guidelines

---

For more detailed information, see the individual component documentation in the `monitoring/` directory.
