groups:
  - name: service_health
    rules:
      # Service Down Alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "Service {{ $labels.instance }} has been down for more than 1 minute."

      # High Error Rate Alerts
      - alert: HighHTTPErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) /
            sum(rate(http_requests_total[5m])) by (service)
          ) * 100 > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High HTTP error rate for {{ $labels.service }}"
          description: "{{ $labels.service }} has HTTP error rate of {{ $value }}% for more than 2 minutes."

      - alert: CriticalHTTPErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) /
            sum(rate(http_requests_total[5m])) by (service)
          ) * 100 > 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical HTTP error rate for {{ $labels.service }}"
          description: "{{ $labels.service }} has HTTP error rate of {{ $value }}% for more than 1 minute."

      # High gRPC Error Rate Alerts
      - alert: HighGRPCErrorRate
        expr: |
          (
            sum(rate(grpc_requests_total{status!="success"}[5m])) by (service) /
            sum(rate(grpc_requests_total[5m])) by (service)
          ) * 100 > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High gRPC error rate for {{ $labels.service }}"
          description: "{{ $labels.service }} has gRPC error rate of {{ $value }}% for more than 2 minutes."

      # Response Time Alerts
      - alert: HighHTTPResponseTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le)
          ) > 1
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High HTTP response time for {{ $labels.service }}"
          description: "{{ $labels.service }} has 95th percentile response time of {{ $value }}s for more than 3 minutes."

      - alert: CriticalHTTPResponseTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le)
          ) > 3
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical HTTP response time for {{ $labels.service }}"
          description: "{{ $labels.service }} has 95th percentile response time of {{ $value }}s for more than 1 minute."

      - alert: HighGRPCResponseTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(grpc_request_duration_seconds_bucket[5m])) by (service, le)
          ) > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High gRPC response time for {{ $labels.service }}"
          description: "{{ $labels.service }} has 95th percentile gRPC response time of {{ $value }}s for more than 3 minutes."

  - name: database_alerts
    rules:
      # Database Connection Alerts
      - alert: HighDatabaseConnections
        expr: |
          sum(database_connections{state="active"}) by (service) > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High database connections for {{ $labels.service }}"
          description: "{{ $labels.service }} has {{ $value }} active database connections."

      - alert: DatabaseConnectionPoolExhausted
        expr: |
          sum(database_connections{state="active"}) by (service) > 95
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection pool nearly exhausted for {{ $labels.service }}"
          description: "{{ $labels.service }} has {{ $value }} active database connections, approaching pool limit."

      # Database Query Performance
      - alert: SlowDatabaseQueries
        expr: |
          histogram_quantile(0.95, 
            sum(rate(database_query_duration_seconds_bucket[5m])) by (service, operation, le)
          ) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow database queries in {{ $labels.service }}"
          description: "{{ $labels.service }} {{ $labels.operation }} has 95th percentile query time of {{ $value }}s."

      - alert: DatabaseErrors
        expr: |
          sum(rate(database_errors_total[5m])) by (service) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database errors in {{ $labels.service }}"
          description: "{{ $labels.service }} is experiencing {{ $value }} database errors per second."

  - name: business_alerts
    rules:
      # Business Operation Alerts
      - alert: HighBusinessErrorRate
        expr: |
          (
            sum(rate(business_errors_total[5m])) by (service, operation) /
            sum(rate(business_operations_total[5m])) by (service, operation)
          ) * 100 > 5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High business error rate for {{ $labels.operation }} in {{ $labels.service }}"
          description: "{{ $labels.service }} {{ $labels.operation }} has error rate of {{ $value }}%."

      # Voucher-specific Alerts
      - alert: VoucherRedemptionFailures
        expr: |
          sum(rate(business_operations_total{operation="voucher_redemption",status!="success"}[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High voucher redemption failures"
          description: "Voucher redemption failures at {{ $value }} per second."

      - alert: VoucherValidationFailures
        expr: |
          sum(rate(business_operations_total{operation="voucher_validation",status!="success"}[5m])) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High voucher validation failures"
          description: "Voucher validation failures at {{ $value }} per second."

      # User Registration Alerts
      - alert: UserRegistrationFailures
        expr: |
          sum(rate(business_operations_total{operation="user_registration",status!="success"}[5m])) > 0.2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High user registration failures"
          description: "User registration failures at {{ $value }} per second."

      # Order Processing Alerts
      - alert: OrderProcessingFailures
        expr: |
          sum(rate(business_operations_total{operation="order_processing",status!="success"}[5m])) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High order processing failures"
          description: "Order processing failures at {{ $value }} per second."

  - name: system_alerts
    rules:
      # Memory Usage Alerts
      - alert: HighMemoryUsage
        expr: |
          (memory_usage_bytes{type="heap"} / (1024*1024*1024)) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage in {{ $labels.service }}"
          description: "{{ $labels.service }} is using {{ $value }}GB of heap memory."

      - alert: CriticalMemoryUsage
        expr: |
          (memory_usage_bytes{type="heap"} / (1024*1024*1024)) > 2
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Critical memory usage in {{ $labels.service }}"
          description: "{{ $labels.service }} is using {{ $value }}GB of heap memory."

      # CPU Usage Alerts
      - alert: HighCPUUsage
        expr: |
          cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage in {{ $labels.service }}"
          description: "{{ $labels.service }} CPU usage is {{ $value }}%."

      - alert: CriticalCPUUsage
        expr: |
          cpu_usage_percent > 95
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Critical CPU usage in {{ $labels.service }}"
          description: "{{ $labels.service }} CPU usage is {{ $value }}%."

  - name: cache_alerts
    rules:
      # Cache Performance Alerts
      - alert: LowCacheHitRate
        expr: |
          (
            sum(rate(cache_operations_total{status="hit"}[5m])) by (service) /
            sum(rate(cache_operations_total[5m])) by (service)
          ) * 100 < 70
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate for {{ $labels.service }}"
          description: "{{ $labels.service }} cache hit rate is {{ $value }}%."

      - alert: HighCacheLatency
        expr: |
          histogram_quantile(0.95, 
            sum(rate(cache_operation_duration_seconds_bucket[5m])) by (service, le)
          ) > 0.1
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High cache latency for {{ $labels.service }}"
          description: "{{ $labels.service }} has 95th percentile cache latency of {{ $value }}s."
