#!/bin/bash

# Setup script for benchmark configuration
# This script helps users create a benchmark configuration file for multi-repository setups

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Script directory
SCRIPT_DIR="$(dirname "$0")"
CONFIG_FILE="$SCRIPT_DIR/benchmark-config.env"
EXAMPLE_FILE="$SCRIPT_DIR/benchmark-config.env.example"

# Services to configure
SERVICES=("auth-service" "user-service" "voucher-service" "product-service" "order-service" "notification-service" "api-gateway")

# Function to prompt for service path
prompt_service_path() {
    local service=$1
    local var_name=$2
    local default_path=$3
    
    echo ""
    print_status "Configuring $service"
    echo "Default path: $default_path"
    read -p "Enter path for $service (press Enter for default): " user_path
    
    if [ -z "$user_path" ]; then
        user_path="$default_path"
    fi
    
    # Expand tilde to home directory
    user_path="${user_path/#\~/$HOME}"
    
    # Check if path exists
    if [ -d "$user_path" ]; then
        print_success "Path exists: $user_path"
        echo "$var_name=\"$user_path\"" >> "$CONFIG_FILE"
    else
        print_warning "Path does not exist: $user_path"
        echo "# WARNING: Path does not exist" >> "$CONFIG_FILE"
        echo "$var_name=\"$user_path\"" >> "$CONFIG_FILE"
    fi
}

# Function to auto-detect service paths
auto_detect_paths() {
    local base_dir="$1"
    local found_services=()
    
    print_status "Auto-detecting services in: $base_dir"
    
    for service in "${SERVICES[@]}"; do
        local service_path="$base_dir/$service"
        local coupon_service_path="$base_dir/coupon-$service"
        
        if [ -d "$service_path" ]; then
            found_services+=("$service:$service_path")
            print_success "Found $service at: $service_path"
        elif [ -d "$coupon_service_path" ]; then
            found_services+=("$service:$coupon_service_path")
            print_success "Found $service at: $coupon_service_path"
        fi
    done
    
    if [ ${#found_services[@]} -gt 0 ]; then
        echo ""
        print_status "Found ${#found_services[@]} services. Use these paths? (y/n)"
        read -p "> " use_detected
        
        if [[ "$use_detected" =~ ^[Yy]$ ]]; then
            for service_info in "${found_services[@]}"; do
                local service="${service_info%%:*}"
                local path="${service_info#*:}"
                local var_name=$(echo "${service^^}" | tr '-' '_')_PATH
                echo "$var_name=\"$path\"" >> "$CONFIG_FILE"
            done
            return 0
        fi
    fi
    
    return 1
}

# Main setup function
main() {
    echo "=================================================="
    echo "  Benchmark Configuration Setup"
    echo "=================================================="
    echo ""
    
    print_status "This script will help you create a benchmark configuration file"
    print_status "for multi-repository microservice setups."
    echo ""
    
    # Check if config file already exists
    if [ -f "$CONFIG_FILE" ]; then
        print_warning "Configuration file already exists: $CONFIG_FILE"
        read -p "Do you want to overwrite it? (y/n): " overwrite
        if [[ ! "$overwrite" =~ ^[Yy]$ ]]; then
            print_status "Setup cancelled."
            exit 0
        fi
        rm "$CONFIG_FILE"
    fi
    
    # Create config file header
    cat > "$CONFIG_FILE" << EOF
# Benchmark Configuration File
# Generated by setup-benchmark-config.sh on $(date)
# 
# This file configures service repository paths for multi-repository setups
# Edit these paths to match your local repository locations

EOF
    
    # Ask for setup method
    echo "Choose setup method:"
    echo "1. Auto-detect services in a directory"
    echo "2. Manual configuration"
    echo "3. Use example template"
    read -p "Enter choice (1-3): " choice
    
    case "$choice" in
        1)
            echo ""
            read -p "Enter base directory to search for services: " base_dir
            base_dir="${base_dir/#\~/$HOME}"
            
            if [ ! -d "$base_dir" ]; then
                print_error "Directory does not exist: $base_dir"
                exit 1
            fi
            
            if auto_detect_paths "$base_dir"; then
                print_success "Auto-detection completed!"
            else
                print_warning "Auto-detection failed or cancelled. Falling back to manual configuration."
                choice=2
            fi
            ;;
        2)
            # Manual configuration - fall through
            ;;
        3)
            if [ -f "$EXAMPLE_FILE" ]; then
                cp "$EXAMPLE_FILE" "$CONFIG_FILE"
                print_success "Example configuration copied to: $CONFIG_FILE"
                print_status "Please edit the file to set your actual service paths."
                exit 0
            else
                print_error "Example file not found: $EXAMPLE_FILE"
                exit 1
            fi
            ;;
        *)
            print_error "Invalid choice. Exiting."
            exit 1
            ;;
    esac
    
    # Manual configuration if needed
    if [ "$choice" = "2" ]; then
        echo ""
        print_status "Manual configuration mode"
        print_status "You will be prompted for each service path."
        
        # Configure each service
        prompt_service_path "auth-service" "AUTH_SERVICE_PATH" "../coupon-auth-service"
        prompt_service_path "user-service" "USER_SERVICE_PATH" "../coupon-user-service"
        prompt_service_path "voucher-service" "VOUCHER_SERVICE_PATH" "../coupon-voucher-service"
        prompt_service_path "product-service" "PRODUCT_SERVICE_PATH" "../coupon-product-service"
        prompt_service_path "order-service" "ORDER_SERVICE_PATH" "../coupon-order-service"
        prompt_service_path "notification-service" "NOTIFICATION_SERVICE_PATH" "../coupon-notification-service"
        prompt_service_path "api-gateway" "API_GATEWAY_PATH" "../coupon-api-gateway"
    fi
    
    # Add optional benchmark settings
    cat >> "$CONFIG_FILE" << EOF

# Optional: Benchmark settings (uncomment to override defaults)
# BENCHMARK_DURATION="30s"
# BENCHMARK_CONCURRENCY="10"
# BENCHMARK_TIMEOUT="30m"
EOF
    
    echo ""
    print_success "Configuration file created: $CONFIG_FILE"
    print_status "You can now run benchmarks with: ./scripts/run-benchmarks.sh"
    print_status "To edit the configuration later, modify: $CONFIG_FILE"
    
    # Offer to show the configuration
    echo ""
    read -p "Do you want to view the generated configuration? (y/n): " show_config
    if [[ "$show_config" =~ ^[Yy]$ ]]; then
        echo ""
        print_status "Generated configuration:"
        echo "----------------------------------------"
        cat "$CONFIG_FILE"
        echo "----------------------------------------"
    fi
}

# Handle script arguments
case "${1:-}" in
    "help")
        echo "Usage: $0 [help]"
        echo ""
        echo "This script helps you create a benchmark configuration file"
        echo "for multi-repository microservice setups."
        echo ""
        echo "The script will:"
        echo "1. Guide you through service path configuration"
        echo "2. Create a benchmark-config.env file"
        echo "3. Validate paths where possible"
        ;;
    *)
        main
        ;;
esac
