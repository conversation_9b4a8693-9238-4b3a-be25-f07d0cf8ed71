# Benchmark Configuration File
# This file allows you to configure service repository paths for multi-repository setups
# Copy this file to 'benchmark-config.env' and customize the paths for your environment

# =============================================================================
# SERVICE REPOSITORY PATHS
# =============================================================================
# Configure the absolute or relative paths to your service repositories
# These paths should point to the root directory of each service repository

# Auth Service Repository Path
# Default: ../coupon-auth-service (relative to benchmark script location)
AUTH_SERVICE_PATH="/path/to/your/auth-service-repo"

# User Service Repository Path  
# Default: ../coupon-user-service
USER_SERVICE_PATH="/path/to/your/user-service-repo"

# Voucher Service Repository Path
# Default: ../coupon-voucher-service  
VOUCHER_SERVICE_PATH="/path/to/your/voucher-service-repo"

# Product Service Repository Path
# Default: ../coupon-product-service
PRODUCT_SERVICE_PATH="/path/to/your/product-service-repo"

# Order Service Repository Path
# Default: ../coupon-order-service
ORDER_SERVICE_PATH="/path/to/your/order-service-repo"

# Notification Service Repository Path
# Default: ../coupon-notification-service
NOTIFICATION_SERVICE_PATH="/path/to/your/notification-service-repo"

# API Gateway Repository Path
# Default: ../coupon-api-gateway
API_GATEWAY_PATH="/path/to/your/api-gateway-repo"

# =============================================================================
# BENCHMARK CONFIGURATION (Optional)
# =============================================================================
# You can also override benchmark settings here instead of using environment variables

# Benchmark duration (e.g., "30s", "1m", "2m30s")
# BENCHMARK_DURATION="30s"

# Benchmark concurrency level (number of concurrent goroutines)
# BENCHMARK_CONCURRENCY="10"

# Test timeout (e.g., "30m", "1h")
# BENCHMARK_TIMEOUT="30m"

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Example 1: All services in a parent directory
# AUTH_SERVICE_PATH="/Users/<USER>/microservices/auth-service"
# USER_SERVICE_PATH="/Users/<USER>/microservices/user-service"
# VOUCHER_SERVICE_PATH="/Users/<USER>/microservices/voucher-service"
# PRODUCT_SERVICE_PATH="/Users/<USER>/microservices/product-service"
# ORDER_SERVICE_PATH="/Users/<USER>/microservices/order-service"
# NOTIFICATION_SERVICE_PATH="/Users/<USER>/microservices/notification-service"
# API_GATEWAY_PATH="/Users/<USER>/microservices/api-gateway"

# Example 2: Services in different locations
# AUTH_SERVICE_PATH="/opt/services/auth"
# USER_SERVICE_PATH="/opt/services/user"
# VOUCHER_SERVICE_PATH="/home/<USER>/voucher-service"
# PRODUCT_SERVICE_PATH="/home/<USER>/product-service"
# ORDER_SERVICE_PATH="/tmp/order-service"
# NOTIFICATION_SERVICE_PATH="/var/services/notification"
# API_GATEWAY_PATH="/usr/local/api-gateway"

# Example 3: Using relative paths from script location
# AUTH_SERVICE_PATH="../../auth-service"
# USER_SERVICE_PATH="../../user-service"
# VOUCHER_SERVICE_PATH="../../voucher-service"
# PRODUCT_SERVICE_PATH="../../product-service"
# ORDER_SERVICE_PATH="../../order-service"
# NOTIFICATION_SERVICE_PATH="../../notification-service"
# API_GATEWAY_PATH="../../api-gateway"
