#!/bin/bash

# Benchmark runner script for multi-repository microservice architecture
set -e

SERVICES=("auth-service" "user-service" "voucher-service" "product-service" "order-service" "notification-service" "api-gateway")
RESULTS_DIR="$(dirname "$0")/../benchmark-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Function to get services to run based on environment variables
get_services_to_run() {
    local services_to_run=()

    # Check for single service flags
    if [ "${AUTH_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("auth-service")
    fi
    if [ "${USER_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("user-service")
    fi
    if [ "${VOUCHER_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("voucher-service")
    fi
    if [ "${PRODUCT_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("product-service")
    fi
    if [ "${ORDER_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("order-service")
    fi
    if [ "${NOTIFICATION_SERVICE_ONLY:-false}" = "true" ]; then
        services_to_run+=("notification-service")
    fi
    if [ "${API_GATEWAY_ONLY:-false}" = "true" ]; then
        services_to_run+=("api-gateway")
    fi

    # If no specific service is requested, run all services
    if [ ${#services_to_run[@]} -eq 0 ]; then
        services_to_run=("${SERVICES[@]}")
    fi

    echo "${services_to_run[@]}"
}

# Configuration file for service repository paths
CONFIG_FILE="${BENCHMARK_CONFIG_FILE:-$(dirname "$0")/benchmark-config.env}"

# Function to get service Git URL
get_service_git_url() {
    local service=$1
    case "$service" in
        "auth-service")
            echo "${AUTH_SERVICE_GIT_URL:-}"
            ;;
        "user-service")
            echo "${USER_SERVICE_GIT_URL:-}"
            ;;
        "voucher-service")
            echo "${VOUCHER_SERVICE_GIT_URL:-}"
            ;;
        "product-service")
            echo "${PRODUCT_SERVICE_GIT_URL:-}"
            ;;
        "order-service")
            echo "${ORDER_SERVICE_GIT_URL:-}"
            ;;
        "notification-service")
            echo "${NOTIFICATION_SERVICE_GIT_URL:-}"
            ;;
        "api-gateway")
            echo "${API_GATEWAY_GIT_URL:-}"
            ;;
        *)
            echo ""
            ;;
    esac
}

# Function to get service path
get_service_path() {
    local service=$1

    # If Git auto-pull is enabled and GIT_BASE_DIR is set, use Git-based paths
    if [ "${GIT_AUTO_PULL:-false}" = "true" ] && [ -n "${GIT_BASE_DIR:-}" ]; then
        echo "${GIT_BASE_DIR}/$service"
    else
        # Use traditional paths
        case "$service" in
            "auth-service")
                echo "${AUTH_SERVICE_PATH:-$(dirname "$0")/../coupon-auth-service}"
                ;;
            "user-service")
                echo "${USER_SERVICE_PATH:-$(dirname "$0")/../coupon-user-service}"
                ;;
            "voucher-service")
                echo "${VOUCHER_SERVICE_PATH:-$(dirname "$0")/../coupon-voucher-service}"
                ;;
            "product-service")
                echo "${PRODUCT_SERVICE_PATH:-$(dirname "$0")/../coupon-product-service}"
                ;;
            "order-service")
                echo "${ORDER_SERVICE_PATH:-$(dirname "$0")/../coupon-order-service}"
                ;;
            "notification-service")
                echo "${NOTIFICATION_SERVICE_PATH:-$(dirname "$0")/../coupon-notification-service}"
                ;;
            "api-gateway")
                echo "${API_GATEWAY_PATH:-$(dirname "$0")/../coupon-api-gateway}"
                ;;
            *)
                echo ""
                ;;
        esac
    fi
}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to setup Git authentication
setup_git_auth() {
    if [ -n "${GIT_USERNAME:-}" ] && [ -n "${GIT_TOKEN:-}" ]; then
        print_status "Setting up Git authentication..."
        # Configure Git to use credentials for HTTPS
        git config --global credential.helper store
        echo "https://${GIT_USERNAME}:${GIT_TOKEN}@gitlab.com" > ~/.git-credentials
        print_success "Git authentication configured"
    fi
}

# Function to clone or update a Git repository
clone_or_update_repo() {
    local service=$1
    local git_url=$(get_service_git_url "$service")
    local service_path=$(get_service_path "$service")
    local branch="${GIT_BRANCH:-main}"

    if [ -z "$git_url" ]; then
        print_warning "No Git URL configured for $service, skipping Git operations"
        return 1
    fi

    print_status "Processing Git repository for $service..."
    print_status "URL: $git_url"
    print_status "Path: $service_path"
    print_status "Branch: $branch"

    # Create base directory if it doesn't exist
    mkdir -p "$(dirname "$service_path")"

    if [ -d "$service_path/.git" ]; then
        # Repository exists, update it
        print_status "Updating existing repository..."
        cd "$service_path"

        # Fetch latest changes
        if git fetch origin; then
            # Reset to latest commit on the specified branch
            if git checkout "$branch" && git reset --hard "origin/$branch"; then
                print_success "Repository updated successfully"
            else
                print_error "Failed to update repository to branch $branch"
                return 1
            fi
        else
            print_error "Failed to fetch from remote repository"
            return 1
        fi
    else
        # Repository doesn't exist, clone it
        print_status "Cloning repository..."
        if git clone -b "$branch" "$git_url" "$service_path"; then
            print_success "Repository cloned successfully"
        else
            print_error "Failed to clone repository"
            return 1
        fi
    fi

    return 0
}

# Function to update all repositories
update_all_repositories() {
    if [ "${GIT_AUTO_PULL:-false}" != "true" ]; then
        print_status "Git auto-pull is disabled, skipping repository updates"
        return 0
    fi

    print_status "Updating all repositories from Git..."

    # Setup Git authentication if needed
    setup_git_auth

    local failed_repos=()
    local current_dir=$(pwd)

    # Update each service repository
    for service in "${SERVICES[@]}"; do
        if ! clone_or_update_repo "$service"; then
            failed_repos+=("$service")
        fi
        cd "$current_dir"
        echo ""
    done

    # Report results
    if [ ${#failed_repos[@]} -eq 0 ]; then
        print_success "All repositories updated successfully"
    else
        print_warning "Failed to update repositories: ${failed_repos[*]}"
        print_warning "Benchmarks may fail for these services"
    fi

    return 0
}

# Function to load configuration from file
load_config() {
    if [ -f "$CONFIG_FILE" ]; then
        print_status "Loading configuration from: $CONFIG_FILE"
        # Source the config file to override default paths
        source "$CONFIG_FILE"
        print_success "Configuration loaded successfully"
    else
        print_status "No configuration file found at $CONFIG_FILE, using default paths"
    fi

    # Print Git configuration
    if [ "${GIT_AUTO_PULL:-false}" = "true" ]; then
        print_status "Git auto-pull: ENABLED"
        print_status "Git base directory: ${GIT_BASE_DIR:-not set}"
        print_status "Git branch: ${GIT_BRANCH:-main}"
    else
        print_status "Git auto-pull: DISABLED"
    fi

    # Print service paths for debugging
    print_status "Service repository paths:"
    for service in "${SERVICES[@]}"; do
        local service_path=$(get_service_path "$service")
        print_status "  $service: $service_path"
    done
}

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create results directory
mkdir -p "$RESULTS_DIR"

print_status "Starting benchmark run at $TIMESTAMP"
print_status "Results will be saved in: $RESULTS_DIR"

# Configuration
BENCHMARK_DURATION=${BENCHMARK_DURATION:-"30s"}
BENCHMARK_CONCURRENCY=${BENCHMARK_CONCURRENCY:-"10"}
BENCHMARK_TIMEOUT=${BENCHMARK_TIMEOUT:-"30m"}

print_status "Configuration:"
print_status "  Duration: $BENCHMARK_DURATION"
print_status "  Concurrency: $BENCHMARK_CONCURRENCY"
print_status "  Timeout: $BENCHMARK_TIMEOUT"

echo ""

# Function to run benchmarks for a service
run_service_benchmarks() {
    local service=$1
    local service_path=$(get_service_path "$service")
    local current_dir=$(pwd)

    print_status "Running benchmarks for $service..."
    print_status "Service path: $service_path"

    # Check if service path exists
    if [ ! -d "$service_path" ]; then
        print_error "Service directory not found: $service_path"
        print_warning "Please check your configuration or set the correct path for $service"
        echo ""
        return 1
    fi

    # Check if benchmark directory exists
    if [ -d "$service_path/benchmark" ]; then
        cd "$service_path"

        # Check if benchmark files exist
        if ls benchmark/*_test.go 1> /dev/null 2>&1; then
            # Create absolute path for results file
            local results_file="$current_dir/$RESULTS_DIR/${service}_benchmark_${TIMESTAMP}.txt"

            # Run benchmarks with custom parameters
            go test -bench=. -benchmem -timeout="$BENCHMARK_TIMEOUT" \
                -benchtime="$BENCHMARK_DURATION" \
                ./benchmark/... > "$results_file" 2>&1

            if [ $? -eq 0 ]; then
                print_success "Benchmark completed for $service"
            else
                print_error "Benchmark failed for $service"
                print_warning "Check $results_file for details"
            fi
        else
            print_warning "No benchmark test files found for $service"
        fi

        # Return to original directory
        cd "$current_dir"
    else
        print_warning "No benchmark directory found for $service at: $service_path/benchmark"
    fi

    echo ""
}

# Function to generate summary report
generate_summary() {
    local summary_file="$RESULTS_DIR/benchmark_summary_${TIMESTAMP}.md"
    
    print_status "Generating benchmark summary..."
    
    cat > "$summary_file" << EOF
# Benchmark Summary Report

**Generated:** $(date)  
**Timestamp:** $TIMESTAMP  
**Configuration:**
- Duration: $BENCHMARK_DURATION
- Concurrency: $BENCHMARK_CONCURRENCY
- Timeout: $BENCHMARK_TIMEOUT

## Service Results

EOF

    for service in "${SERVICES[@]}"; do
        local result_file="$RESULTS_DIR/${service}_benchmark_${TIMESTAMP}.txt"
        
        echo "### $service" >> "$summary_file"
        echo "" >> "$summary_file"
        
        if [ -f "$result_file" ]; then
            # Extract key metrics from benchmark results
            echo '```' >> "$summary_file"
            
            # Get benchmark results (filter out verbose output)
            grep "^Benchmark" "$result_file" | head -10 >> "$summary_file" 2>/dev/null || echo "No benchmark results found" >> "$summary_file"
            
            echo '```' >> "$summary_file"
            echo "" >> "$summary_file"
            
            # Check for errors
            if grep -q "FAIL" "$result_file"; then
                echo "⚠️ **Status:** Some tests failed" >> "$summary_file"
            else
                echo "✅ **Status:** All tests passed" >> "$summary_file"
            fi
        else
            echo "❌ **Status:** No results file found" >> "$summary_file"
        fi
        
        echo "" >> "$summary_file"
    done
    
    cat >> "$summary_file" << EOF

## Performance Analysis

### Top Performing Operations
EOF

    # Find fastest operations across all services
    for service in "${SERVICES[@]}"; do
        local result_file="$RESULTS_DIR/${service}_benchmark_${TIMESTAMP}.txt"
        if [ -f "$result_file" ]; then
            echo "#### $service" >> "$summary_file"
            echo '```' >> "$summary_file"
            grep "^Benchmark" "$result_file" | sort -k3 -n | head -3 >> "$summary_file" 2>/dev/null || echo "No data available" >> "$summary_file"
            echo '```' >> "$summary_file"
            echo "" >> "$summary_file"
        fi
    done
    
    cat >> "$summary_file" << EOF

## Recommendations

1. **Review slow operations** - Focus on operations taking >1s
2. **Memory optimization** - Check operations with high memory allocation
3. **Concurrency testing** - Run load tests for critical paths
4. **Cache optimization** - Improve cache hit rates where applicable

---
*Generated by Coupon System Benchmark Runner*
EOF

    print_success "Summary report generated: $summary_file"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if Go is installed
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed or not in PATH"
        exit 1
    fi

    # Check if Git is installed (required for Git operations)
    if [ "${GIT_AUTO_PULL:-false}" = "true" ] && ! command -v git &> /dev/null; then
        print_error "Git is not installed or not in PATH (required for Git auto-pull)"
        exit 1
    fi

    # Check if we're in the right directory (only if not using Git auto-pull)
    if [ "${GIT_AUTO_PULL:-false}" != "true" ]; then
        if [ ! -f "docker-compose.yml" ] && [ ! -f "docker-compose.monitoring.yml" ]; then
            print_error "Please run this script from the project root directory"
            exit 1
        fi
    fi

    print_success "Prerequisites check passed"
}

# Function to check if services are running
check_services() {
    print_status "Checking if services are running..."
    
    local services_running=0
    local ports=(50051 50053 50054 50055 50056 50057 8080)
    
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            ((services_running++))
        fi
    done
    
    if [ $services_running -gt 0 ]; then
        print_success "$services_running services appear to be running"
    else
        print_warning "No services appear to be running on expected ports"
        print_warning "Benchmarks may fail if services are not accessible"
        print_status "To start services: docker-compose up -d"
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "  Coupon System - Benchmark Runner"
    echo "=================================================="
    echo ""

    # Load configuration first
    load_config
    echo ""

    check_prerequisites

    # Update repositories from Git if enabled
    update_all_repositories
    echo ""

    check_services

    echo ""

    # Get services to run (all or filtered)
    local services_to_run=($(get_services_to_run))

    print_status "Services to benchmark: ${services_to_run[*]}"
    echo ""

    # Run benchmarks for each service
    for service in "${services_to_run[@]}"; do
        run_service_benchmarks "$service"
    done

    # Generate summary report
    generate_summary

    print_success "All benchmarks completed!"
    print_status "Results saved in: $RESULTS_DIR"
    print_status "Summary report: $RESULTS_DIR/benchmark_summary_${TIMESTAMP}.md"

    echo ""
    echo "Next steps:"
    echo "1. Review the summary report for performance insights"
    echo "2. Check individual service results for detailed metrics"
    echo "3. Compare with previous benchmark runs for regression analysis"
    echo "4. Use 'make -f Makefile.monitoring benchmark-report' for formatted reports"
}

# Handle script arguments
case "${1:-}" in
    "quick")
        BENCHMARK_DURATION="10s"
        BENCHMARK_TIMEOUT="10m"
        print_status "Running quick benchmarks..."
        main
        ;;
    "load")
        BENCHMARK_DURATION="60s"
        BENCHMARK_CONCURRENCY="20"
        BENCHMARK_TIMEOUT="60m"
        print_status "Running load benchmarks..."
        main
        ;;
    "stress")
        BENCHMARK_DURATION="120s"
        BENCHMARK_CONCURRENCY="50"
        BENCHMARK_TIMEOUT="120m"
        print_status "Running stress benchmarks..."
        main
        ;;
    "help")
        echo "Usage: $0 [quick|load|stress|help]"
        echo ""
        echo "Options:"
        echo "  quick  - Run quick benchmarks (10s duration)"
        echo "  load   - Run load benchmarks (60s duration, 20 concurrency)"
        echo "  stress - Run stress benchmarks (120s duration, 50 concurrency)"
        echo "  help   - Show this help message"
        echo ""
        echo "Environment variables:"
        echo "  BENCHMARK_DURATION    - Benchmark duration (default: 30s)"
        echo "  BENCHMARK_CONCURRENCY - Concurrency level (default: 10)"
        echo "  BENCHMARK_TIMEOUT     - Test timeout (default: 30m)"
        echo "  BENCHMARK_CONFIG_FILE - Path to configuration file (default: scripts/benchmark-config.env)"
        echo ""
        echo "Git configuration variables:"
        echo "  GIT_AUTO_PULL    - Enable automatic Git clone/pull (true/false)"
        echo "  GIT_BASE_DIR     - Base directory for cloned repositories"
        echo "  GIT_BRANCH       - Git branch to checkout (default: main)"
        echo "  GIT_USERNAME     - GitLab username (for private repos)"
        echo "  GIT_TOKEN        - GitLab access token (for private repos)"
        echo ""
        echo "Service Git URL variables:"
        echo "  AUTH_SERVICE_GIT_URL         - GitLab URL for auth service"
        echo "  USER_SERVICE_GIT_URL         - GitLab URL for user service"
        echo "  VOUCHER_SERVICE_GIT_URL      - GitLab URL for voucher service"
        echo "  PRODUCT_SERVICE_GIT_URL      - GitLab URL for product service"
        echo "  ORDER_SERVICE_GIT_URL        - GitLab URL for order service"
        echo "  NOTIFICATION_SERVICE_GIT_URL - GitLab URL for notification service"
        echo "  API_GATEWAY_GIT_URL          - GitLab URL for API gateway"
        echo ""
        echo "Service path environment variables (for local repositories):"
        echo "  AUTH_SERVICE_PATH         - Path to auth service repository"
        echo "  USER_SERVICE_PATH         - Path to user service repository"
        echo "  VOUCHER_SERVICE_PATH      - Path to voucher service repository"
        echo "  PRODUCT_SERVICE_PATH      - Path to product service repository"
        echo "  ORDER_SERVICE_PATH        - Path to order service repository"
        echo "  NOTIFICATION_SERVICE_PATH - Path to notification service repository"
        echo "  API_GATEWAY_PATH          - Path to API gateway repository"
        echo ""
        echo "Configuration file format (benchmark-config.env):"
        echo "  # Git configuration"
        echo "  GIT_AUTO_PULL=\"true\""
        echo "  GIT_BASE_DIR=\"/tmp/benchmark-repos\""
        echo "  AUTH_SERVICE_GIT_URL=\"https://gitlab.com/group/auth-service.git\""
        echo "  # ... etc for other services"
        ;;
    *)
        main
        ;;
esac
