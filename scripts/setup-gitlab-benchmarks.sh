#!/bin/bash

# GitLab Benchmark Setup Script
# This script helps you quickly configure benchmarks for GitLab-hosted repositories

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Script directory
SCRIPT_DIR="$(dirname "$0")"
CONFIG_FILE="$SCRIPT_DIR/benchmark-config.env"
GITLAB_TEMPLATE="$SCRIPT_DIR/benchmark-config-gitlab.env.example"

# Services to configure
SERVICES=("auth-service" "user-service" "voucher-service" "product-service" "order-service" "notification-service" "api-gateway")

# Function to validate GitLab URL
validate_gitlab_url() {
    local url=$1
    if [[ "$url" =~ ^https://gitlab\.com/.+/.+\.git$ ]] || [[ "$url" =~ ^git@gitlab\.com:.+/.+\.git$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to test Git repository access
test_repo_access() {
    local url=$1
    local temp_dir=$(mktemp -d)
    
    print_status "Testing access to repository: $url"
    
    if git ls-remote "$url" HEAD &>/dev/null; then
        print_success "Repository is accessible"
        rm -rf "$temp_dir"
        return 0
    else
        print_error "Cannot access repository"
        rm -rf "$temp_dir"
        return 1
    fi
}

# Main setup function
main() {
    echo "=================================================="
    echo "  GitLab Benchmark Configuration Setup"
    echo "=================================================="
    echo ""
    
    print_status "This script will help you configure benchmarks for GitLab repositories."
    echo ""
    
    # Check if config file already exists
    if [ -f "$CONFIG_FILE" ]; then
        print_warning "Configuration file already exists: $CONFIG_FILE"
        read -p "Do you want to overwrite it? (y/n): " overwrite
        if [[ ! "$overwrite" =~ ^[Yy]$ ]]; then
            print_status "Setup cancelled."
            exit 0
        fi
        rm "$CONFIG_FILE"
    fi
    
    # Gather basic information
    echo "=== Basic Configuration ==="
    
    # GitLab group/username
    read -p "Enter your GitLab group or username: " gitlab_group
    if [ -z "$gitlab_group" ]; then
        print_error "GitLab group/username is required"
        exit 1
    fi
    
    # Base directory
    read -p "Enter base directory for repositories [/tmp/benchmark-repos]: " base_dir
    base_dir="${base_dir:-/tmp/benchmark-repos}"
    base_dir="${base_dir/#\~/$HOME}"
    
    # Git branch
    read -p "Enter Git branch to test [main]: " git_branch
    git_branch="${git_branch:-main}"
    
    # Authentication method
    echo ""
    echo "=== Authentication Method ==="
    echo "1. Public repositories (no authentication)"
    echo "2. Private repositories with Personal Access Token"
    echo "3. SSH key authentication"
    read -p "Choose authentication method (1-3): " auth_method
    
    local git_username=""
    local git_token=""
    local use_ssh=false
    
    case "$auth_method" in
        1)
            print_status "Using public repository access"
            ;;
        2)
            read -p "Enter your GitLab username: " git_username
            read -s -p "Enter your GitLab Personal Access Token: " git_token
            echo ""
            if [ -z "$git_username" ] || [ -z "$git_token" ]; then
                print_error "Username and token are required for private repositories"
                exit 1
            fi
            ;;
        3)
            print_status "Using SSH key authentication"
            use_ssh=true
            # Test SSH connection
            if ! ssh -T ************** 2>&1 | grep -q "Welcome to GitLab"; then
                print_warning "SSH connection to GitLab failed. Please ensure your SSH keys are configured."
                read -p "Continue anyway? (y/n): " continue_ssh
                if [[ ! "$continue_ssh" =~ ^[Yy]$ ]]; then
                    exit 1
                fi
            fi
            ;;
        *)
            print_error "Invalid choice"
            exit 1
            ;;
    esac
    
    # Create configuration file
    print_status "Creating configuration file..."
    
    cat > "$CONFIG_FILE" << EOF
# GitLab Benchmark Configuration
# Generated by setup-gitlab-benchmarks.sh on $(date)

# Git configuration
GIT_AUTO_PULL="true"
GIT_BASE_DIR="$base_dir"
GIT_BRANCH="$git_branch"

EOF
    
    # Add authentication if needed
    if [ "$auth_method" = "2" ]; then
        cat >> "$CONFIG_FILE" << EOF
# GitLab authentication
GIT_USERNAME="$git_username"
GIT_TOKEN="$git_token"

EOF
    fi
    
    # Add repository URLs
    echo "# GitLab repository URLs" >> "$CONFIG_FILE"
    
    for service in "${SERVICES[@]}"; do
        if [ "$use_ssh" = true ]; then
            echo "${service^^}_GIT_URL=\"**************:${gitlab_group}/${service}.git\"" | tr '-' '_' >> "$CONFIG_FILE"
        else
            echo "${service^^}_GIT_URL=\"https://gitlab.com/${gitlab_group}/${service}.git\"" | tr '-' '_' >> "$CONFIG_FILE"
        fi
    done
    
    # Add benchmark settings
    cat >> "$CONFIG_FILE" << EOF

# Benchmark settings
BENCHMARK_DURATION="30s"
BENCHMARK_CONCURRENCY="10"
BENCHMARK_TIMEOUT="30m"
EOF
    
    print_success "Configuration file created: $CONFIG_FILE"
    
    # Test repository access
    echo ""
    print_status "Testing repository access..."
    
    local accessible_repos=0
    local total_repos=${#SERVICES[@]}
    
    for service in "${SERVICES[@]}"; do
        local url
        if [ "$use_ssh" = true ]; then
            url="**************:${gitlab_group}/${service}.git"
        else
            url="https://gitlab.com/${gitlab_group}/${service}.git"
        fi
        
        if test_repo_access "$url"; then
            ((accessible_repos++))
        fi
    done
    
    echo ""
    print_status "Repository access test results: $accessible_repos/$total_repos repositories accessible"
    
    if [ $accessible_repos -eq $total_repos ]; then
        print_success "All repositories are accessible!"
    elif [ $accessible_repos -gt 0 ]; then
        print_warning "Some repositories are not accessible. Benchmarks may fail for those services."
    else
        print_error "No repositories are accessible. Please check your configuration."
        print_status "Common issues:"
        print_status "- Incorrect GitLab group/username"
        print_status "- Invalid authentication credentials"
        print_status "- Repositories don't exist or are private"
        print_status "- Network connectivity issues"
    fi
    
    # Offer to run benchmarks
    echo ""
    if [ $accessible_repos -gt 0 ]; then
        read -p "Do you want to run a quick benchmark test now? (y/n): " run_test
        if [[ "$run_test" =~ ^[Yy]$ ]]; then
            print_status "Running quick benchmark test..."
            "$SCRIPT_DIR/run-benchmarks.sh" quick
        fi
    fi
    
    echo ""
    print_success "Setup completed!"
    print_status "Configuration saved to: $CONFIG_FILE"
    print_status "To run benchmarks: ./scripts/run-benchmarks.sh"
    print_status "To edit configuration: edit $CONFIG_FILE"
}

# Handle script arguments
case "${1:-}" in
    "help")
        echo "Usage: $0 [help]"
        echo ""
        echo "This script helps you configure benchmarks for GitLab-hosted repositories."
        echo ""
        echo "The script will:"
        echo "1. Collect your GitLab group/username"
        echo "2. Configure authentication (public, token, or SSH)"
        echo "3. Set up repository URLs for all services"
        echo "4. Test repository access"
        echo "5. Create a benchmark-config.env file"
        echo ""
        echo "Prerequisites:"
        echo "- Git installed and configured"
        echo "- Access to GitLab repositories"
        echo "- For private repos: Personal Access Token or SSH keys"
        ;;
    *)
        main
        ;;
esac
