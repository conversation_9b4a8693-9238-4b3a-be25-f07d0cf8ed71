# Example GitLab Configuration for Benchmark Runner
# This is a working example - replace with your actual GitLab details

# =============================================================================
# GIT CONFIGURATION
# =============================================================================

# Enable automatic Git operations
GIT_AUTO_PULL="true"

# Base directory for cloned repositories
GIT_BASE_DIR="/tmp/benchmark-repos"

# Git branch to test
GIT_BRANCH="main"

# =============================================================================
# GITLAB AUTHENTICATION (uncomment and configure for private repos)
# =============================================================================

# For private repositories, uncomment and set your credentials:
# GIT_USERNAME="your-gitlab-username"
# GIT_TOKEN="glpat-xxxxxxxxxxxxxxxxxxxx"

# =============================================================================
# EXAMPLE GITLAB REPOSITORY URLS
# =============================================================================
# Replace 'your-company' with your actual GitLab group/organization

# HTTPS URLs (for public repos or with username/token auth)
AUTH_SERVICE_GIT_URL="https://gitlab.com/your-company/auth-service.git"
USER_SERVICE_GIT_URL="https://gitlab.com/your-company/user-service.git"
VOUCHER_SERVICE_GIT_URL="https://gitlab.com/your-company/voucher-service.git"
PRODUCT_SERVICE_GIT_URL="https://gitlab.com/your-company/product-service.git"
ORDER_SERVICE_GIT_URL="https://gitlab.com/your-company/order-service.git"
NOTIFICATION_SERVICE_GIT_URL="https://gitlab.com/your-company/notification-service.git"
API_GATEWAY_GIT_URL="https://gitlab.com/your-company/api-gateway.git"

# Alternative: SSH URLs (if you have SSH keys configured)
# AUTH_SERVICE_GIT_URL="**************:your-company/auth-service.git"
# USER_SERVICE_GIT_URL="**************:your-company/user-service.git"
# VOUCHER_SERVICE_GIT_URL="**************:your-company/voucher-service.git"
# PRODUCT_SERVICE_GIT_URL="**************:your-company/product-service.git"
# ORDER_SERVICE_GIT_URL="**************:your-company/order-service.git"
# NOTIFICATION_SERVICE_GIT_URL="**************:your-company/notification-service.git"
# API_GATEWAY_GIT_URL="**************:your-company/api-gateway.git"

# =============================================================================
# BENCHMARK SETTINGS
# =============================================================================

# Benchmark duration
BENCHMARK_DURATION="30s"

# Concurrency level
BENCHMARK_CONCURRENCY="10"

# Test timeout
BENCHMARK_TIMEOUT="30m"

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================
# 
# 1. Copy this file to 'benchmark-config.env':
#    cp scripts/example-gitlab-config.env scripts/benchmark-config.env
#
# 2. Edit the file and replace 'your-company' with your GitLab group
#
# 3. For private repositories, uncomment and set GIT_USERNAME and GIT_TOKEN
#
# 4. Run benchmarks:
#    ./scripts/run-benchmarks.sh
#
# The script will:
# - Clone or update repositories from GitLab
# - Switch to the specified branch
# - Run benchmarks on each service
# - Generate performance reports
