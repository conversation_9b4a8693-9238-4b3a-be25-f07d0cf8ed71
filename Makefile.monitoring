# Coupon System - Monitoring and Benchmarking Makefile
# This Makefile provides convenient commands for managing monitoring and benchmarking

.PHONY: help monitoring-deploy monitoring-start monitoring-stop monitoring-restart monitoring-status monitoring-logs monitoring-clean benchmark-all benchmark-user benchmark-voucher benchmark-product benchmark-auth benchmark-api-gateway benchmark-order benchmark-notification benchmark-report benchmark-clean monitoring-backup monitoring-restore

# Default target
help: ## Show this help message
	@echo "Coupon System - Monitoring and Benchmarking Commands"
	@echo "=================================================="
	@echo ""
	@echo "Monitoring Commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep "monitoring-"
	@echo ""
	@echo "Benchmark Commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep "benchmark-"
	@echo ""
	@echo "Utility Commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -v -E "(monitoring-|benchmark-)"

# Variables
TIMESTAMP := $(shell date +%Y%m%d_%H%M%S)
RESULTS_DIR := benchmark-results
BACKUP_DIR := monitoring-backups
SERVICES := user-service voucher-service product-service auth-service api-gateway order-service notification-service

# Monitoring Commands
monitoring-deploy: ## Deploy the complete monitoring stack
	@echo "Deploying monitoring stack..."
	@chmod +x scripts/deploy-monitoring.sh
	@./scripts/deploy-monitoring.sh

monitoring-start: ## Start monitoring services
	@echo "Starting monitoring services..."
	@./scripts/deploy-monitoring.sh start

monitoring-stop: ## Stop monitoring services
	@echo "Stopping monitoring services..."
	@./scripts/deploy-monitoring.sh stop

monitoring-restart: ## Restart monitoring services
	@echo "Restarting monitoring services..."
	@./scripts/deploy-monitoring.sh restart

monitoring-status: ## Show monitoring services status
	@echo "Monitoring services status:"
	@./scripts/deploy-monitoring.sh status

monitoring-logs: ## Show monitoring services logs (use SERVICE=name for specific service)
	@echo "Showing monitoring logs..."
	@./scripts/deploy-monitoring.sh logs $(SERVICE)

monitoring-health: ## Run health checks on monitoring services
	@echo "Running monitoring health checks..."
	@./scripts/deploy-monitoring.sh health

monitoring-clean: ## Clean up monitoring data and containers
	@echo "Cleaning up monitoring stack..."
	@docker-compose -f docker-compose.monitoring.yml down -v
	@docker system prune -f
	@echo "Monitoring cleanup completed"

# Benchmark Commands
benchmark-all: ## Run benchmarks for all services
	@echo "Running benchmarks for all services..."
	@mkdir -p $(RESULTS_DIR)
	@chmod +x scripts/run-benchmarks.sh
	@./scripts/run-benchmarks.sh
	@echo "All benchmarks completed. Results in $(RESULTS_DIR)/"

benchmark-user: ## Run benchmarks for user service
	@echo "Running user service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@cd coupon-user-service && go test -bench=. -benchmem -timeout=30m ./benchmark/... > ../$(RESULTS_DIR)/user-service_$(TIMESTAMP).txt 2>&1
	@echo "User service benchmarks completed"

benchmark-voucher: ## Run benchmarks for voucher service
	@echo "Running voucher service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@cd coupon-voucher-service && go test -bench=. -benchmem -timeout=30m ./benchmark/... > ../$(RESULTS_DIR)/voucher-service_$(TIMESTAMP).txt 2>&1
	@echo "Voucher service benchmarks completed"

benchmark-product: ## Run benchmarks for product service
	@echo "Running product service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@cd coupon-product-service && go test -bench=. -benchmem -timeout=30m ./benchmark/... > ../$(RESULTS_DIR)/product-service_$(TIMESTAMP).txt 2>&1
	@echo "Product service benchmarks completed"

benchmark-auth: ## Run benchmarks for auth service
	@echo "Running auth service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@cd coupon-auth-service && go test -bench=. -benchmem -timeout=30m ./benchmark/... > ../$(RESULTS_DIR)/auth-service_$(TIMESTAMP).txt 2>&1
	@echo "Auth service benchmarks completed"

benchmark-api-gateway: ## Run benchmarks for API gateway
	@echo "Running API gateway benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@cd coupon-api-gateway && go test -bench=. -benchmem -timeout=30m ./benchmark/... > ../$(RESULTS_DIR)/api-gateway_$(TIMESTAMP).txt 2>&1
	@echo "API gateway benchmarks completed"

benchmark-order: ## Run benchmarks for order service
	@echo "Running order service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@cd coupon-order-service && go test -bench=. -benchmem -timeout=30m ./benchmark/... > ../$(RESULTS_DIR)/order-service_$(TIMESTAMP).txt 2>&1
	@echo "Order service benchmarks completed"

benchmark-notification: ## Run benchmarks for notification service
	@echo "Running notification service benchmarks..."
	@mkdir -p $(RESULTS_DIR)
	@cd coupon-notification-service && go test -bench=. -benchmem -timeout=30m ./benchmark/... > ../$(RESULTS_DIR)/notification-service_$(TIMESTAMP).txt 2>&1
	@echo "Notification service benchmarks completed"

benchmark-load: ## Run load testing scenarios
	@echo "Running load testing scenarios..."
	@mkdir -p $(RESULTS_DIR)
	@for service in $(SERVICES); do \
		echo "Running load tests for $$service..."; \
		cd coupon-$$service && go test -bench=.*Load.* -benchmem -timeout=60m ./benchmark/... > ../$(RESULTS_DIR)/$$service-load_$(TIMESTAMP).txt 2>&1; \
		cd ..; \
	done
	@echo "Load testing completed"

benchmark-stress: ## Run stress testing scenarios
	@echo "Running stress testing scenarios..."
	@mkdir -p $(RESULTS_DIR)
	@for service in $(SERVICES); do \
		echo "Running stress tests for $$service..."; \
		cd coupon-$$service && go test -bench=.*Stress.* -benchmem -timeout=60m ./benchmark/... > ../$(RESULTS_DIR)/$$service-stress_$(TIMESTAMP).txt 2>&1; \
		cd ..; \
	done
	@echo "Stress testing completed"

benchmark-report: ## Generate benchmark report
	@echo "Generating benchmark report..."
	@mkdir -p $(RESULTS_DIR)
	@echo "# Benchmark Report - $(TIMESTAMP)" > $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "## Summary" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "Generated: $(shell date)" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md
	@for file in $(RESULTS_DIR)/*_$(TIMESTAMP).txt; do \
		if [ -f "$$file" ]; then \
			echo "### $$(basename $$file .txt)" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo '```' >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			tail -20 "$$file" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo '```' >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
			echo "" >> $(RESULTS_DIR)/report_$(TIMESTAMP).md; \
		fi \
	done
	@echo "Benchmark report generated: $(RESULTS_DIR)/report_$(TIMESTAMP).md"

benchmark-clean: ## Clean up benchmark results
	@echo "Cleaning up benchmark results..."
	@rm -rf $(RESULTS_DIR)
	@echo "Benchmark results cleaned"

benchmark-compare: ## Compare benchmark results (requires BASELINE and CURRENT files)
	@echo "Comparing benchmark results..."
	@if [ -z "$(BASELINE)" ] || [ -z "$(CURRENT)" ]; then \
		echo "Usage: make benchmark-compare BASELINE=file1.txt CURRENT=file2.txt"; \
		exit 1; \
	fi
	@echo "Baseline: $(BASELINE)"
	@echo "Current:  $(CURRENT)"
	@echo "Comparison results:"
	@./scripts/compare-benchmarks.sh $(BASELINE) $(CURRENT)

# Backup and Restore Commands
monitoring-backup: ## Backup monitoring data
	@echo "Backing up monitoring data..."
	@mkdir -p $(BACKUP_DIR)
	@docker run --rm -v prometheus-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/prometheus-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v grafana-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/grafana-$(TIMESTAMP).tar.gz /data
	@docker run --rm -v alertmanager-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar czf /backup/alertmanager-$(TIMESTAMP).tar.gz /data
	@echo "Monitoring data backed up to $(BACKUP_DIR)/"

monitoring-restore: ## Restore monitoring data (requires BACKUP_DATE)
	@echo "Restoring monitoring data..."
	@if [ -z "$(BACKUP_DATE)" ]; then \
		echo "Usage: make monitoring-restore BACKUP_DATE=20231201_120000"; \
		exit 1; \
	fi
	@./scripts/deploy-monitoring.sh stop
	@docker run --rm -v prometheus-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/prometheus-$(BACKUP_DATE).tar.gz -C /
	@docker run --rm -v grafana-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/grafana-$(BACKUP_DATE).tar.gz -C /
	@docker run --rm -v alertmanager-data:/data -v $(PWD)/$(BACKUP_DIR):/backup alpine tar xzf /backup/alertmanager-$(BACKUP_DATE).tar.gz -C /
	@./scripts/deploy-monitoring.sh start
	@echo "Monitoring data restored from $(BACKUP_DATE)"

# Development Commands
dev-setup: ## Setup development environment for monitoring
	@echo "Setting up development environment..."
	@go mod tidy
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install golang.org/x/tools/cmd/goimports@latest
	@echo "Development environment setup completed"

dev-test: ## Run tests for monitoring components
	@echo "Running monitoring component tests..."
	@go test -v ./coupon-shared-libs/metrics/...
	@go test -v ./coupon-shared-libs/benchmark/...
	@echo "Tests completed"

dev-lint: ## Run linting on monitoring code
	@echo "Running linting..."
	@golangci-lint run ./coupon-shared-libs/metrics/...
	@golangci-lint run ./coupon-shared-libs/benchmark/...
	@echo "Linting completed"

# Utility Commands
urls: ## Show monitoring service URLs
	@echo "Monitoring Service URLs:"
	@echo "  Grafana:      http://localhost:3000 (admin/admin123)"
	@echo "  Prometheus:   http://localhost:9090"
	@echo "  AlertManager: http://localhost:9093"
	@echo "  Node Exporter: http://localhost:9100"
	@echo "  cAdvisor:     http://localhost:8080"

dashboard-import: ## Import additional Grafana dashboards
	@echo "Importing Grafana dashboards..."
	@for dashboard in monitoring/grafana/dashboards/*.json; do \
		echo "Importing $$(basename $$dashboard)..."; \
		curl -X POST \
			-H "Content-Type: application/json" \
			-u admin:admin123 \
			-d @$$dashboard \
			http://localhost:3000/api/dashboards/db; \
		echo ""; \
	done
	@echo "Dashboard import completed"

metrics-test: ## Test metrics endpoints
	@echo "Testing metrics endpoints..."
	@for service in $(SERVICES); do \
		echo "Testing coupon-$$service metrics..."; \
		curl -s http://localhost:2112/metrics | head -5 || echo "Service not available"; \
		echo ""; \
	done

performance-baseline: ## Establish performance baselines
	@echo "Establishing performance baselines..."
	@mkdir -p $(RESULTS_DIR)/baselines
	@make benchmark-all
	@cp $(RESULTS_DIR)/*_$(TIMESTAMP).txt $(RESULTS_DIR)/baselines/
	@echo "Performance baselines established in $(RESULTS_DIR)/baselines/"

ci-benchmark: ## Run benchmarks suitable for CI/CD
	@echo "Running CI benchmark suite..."
	@mkdir -p $(RESULTS_DIR)
	@for service in $(SERVICES); do \
		echo "Running CI benchmarks for $$service..."; \
		cd coupon-$$service && go test -bench=. -benchmem -short -timeout=10m ./benchmark/... > ../$(RESULTS_DIR)/ci-$$service_$(TIMESTAMP).txt 2>&1; \
		cd ..; \
	done
	@echo "CI benchmarks completed"

# Clean up commands
clean-all: monitoring-clean benchmark-clean ## Clean up everything
	@echo "Cleaning up all monitoring and benchmark data..."
	@rm -rf $(BACKUP_DIR)
	@echo "Complete cleanup finished"
