#!/bin/bash

# Script to fix common benchmark issues across all services
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to add helper methods to benchmark framework
add_helper_methods() {
    print_status "Adding helper methods to benchmark framework..."
    
    cat >> coupon-shared-libs/benchmark/framework.go << 'EOF'

// Helper methods for generating test data
func (tdg *TestDataGenerator) GenerateUserID() int {
	return tdg.rand.Intn(1000) + 1
}

func (tdg *TestDataGenerator) GenerateProductID() int {
	return tdg.rand.Intn(1000) + 1
}

func (tdg *TestDataGenerator) GenerateOrderID() int {
	return tdg.rand.Intn(1000) + 1
}

func (tdg *TestDataGenerator) GenerateVoucherID() int {
	return tdg.rand.Intn(1000) + 1
}

func (tdg *TestDataGenerator) GenerateNotificationID() int {
	return tdg.rand.Intn(1000) + 1
}

func (tdg *TestDataGenerator) GenerateCategoryID() int {
	return tdg.rand.Intn(10) + 1
}

func (tdg *TestDataGenerator) GenerateQuantity() int {
	return tdg.rand.Intn(100) + 1
}

func (tdg *TestDataGenerator) GeneratePrice() float64 {
	return tdg.rand.Float64() * 1000 // $0-1000
}

func (tdg *TestDataGenerator) GenerateVoucherCode() string {
	return fmt.Sprintf("VOUCHER_%d", tdg.rand.Intn(10000))
}

func (tdg *TestDataGenerator) GenerateUnreadCount() int {
	return tdg.rand.Intn(50)
}

func (tdg *TestDataGenerator) GenerateOrderData() map[string]interface{} {
	return map[string]interface{}{
		"id":           tdg.GenerateOrderID(),
		"user_id":      tdg.GenerateUserID(),
		"total_amount": tdg.GeneratePrice(),
		"status":       "PENDING",
		"voucher_code": tdg.GenerateVoucherCode(),
		"created_at":   time.Now(),
	}
}

func (tdg *TestDataGenerator) GenerateNotificationData() map[string]interface{} {
	return map[string]interface{}{
		"id":         tdg.GenerateNotificationID(),
		"user_id":    tdg.GenerateUserID(),
		"type":       "VOUCHER_CREATED",
		"title":      fmt.Sprintf("Notification %d", tdg.GenerateNotificationID()),
		"message":    "Test notification message",
		"status":     "PENDING",
		"created_at": time.Now(),
	}
}
EOF

    print_success "Helper methods added to benchmark framework"
}

# Function to create a simple working benchmark template
create_simple_benchmark() {
    local service=$1
    local service_dir="coupon-$service"
    local proto_package=$2
    
    print_status "Creating simple benchmark for $service..."
    
    mkdir -p "$service_dir/benchmark"
    
    cat > "$service_dir/benchmark/${service}_benchmark_test.go" << EOF
package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

var (
	grpcSuite     *benchmark.GRPCBenchmarkSuite
	dbSuite       *benchmark.DatabaseBenchmarkSuite
	cacheSuite    *benchmark.CacheBenchmarkSuite
	testDB        *database.DB
	testRedis     *redis.Client
	testGenerator *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup $service benchmark environment: %v\n", err)
		os.Exit(1)
	}
	
	code := m.Run()
	cleanup()
	os.Exit(code)
}

func setupBenchmarkEnvironment() error {
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("$service-benchmark")
	
	testDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	testRedis = redis.NewClient(&cfg.Redis, logger, appMetrics)

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50051", // Adjust port as needed
		ServiceName:    "$service",
		Timeout:        5 * time.Second,
	}
	
	grpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "$service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	dbSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, testDB.DB)

	cacheSuite = benchmark.NewCacheBenchmarkSuite("$service", testRedis)
	testGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanup() {
	if grpcSuite != nil {
		grpcSuite.Close()
	}
	if testDB != nil {
		sqlDB, _ := testDB.DB.DB()
		sqlDB.Close()
	}
	if testRedis != nil {
		testRedis.Close()
	}
}

// Basic gRPC benchmark
func Benchmark${service^}_gRPC_HealthCheck(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "$service",
		TestName:       "grpc_health_check",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		// Create a simple gRPC connection test
		conn, err := grpc.DialContext(ctx, "localhost:50051", 
			grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			return err
		}
		defer conn.Close()
		return nil
	}

	framework.RunBenchmark(b, operation)
}

// Database benchmark
func Benchmark${service^}_Database_SimpleQuery(b *testing.B) {
	dbSuite.BenchmarkQuery(b, "simple_query", func(ctx context.Context, db *gorm.DB) error {
		var count int64
		return db.Raw("SELECT 1").Count(&count).Error
	})
}

// Cache benchmark
func Benchmark${service^}_Cache_SetGet(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "$service",
		TestName:       "cache_set_get",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		key := fmt.Sprintf("test_key_%d", testGenerator.GenerateUserID())
		value := "test_value"
		
		// Set
		if err := testRedis.Set(ctx, key, value, time.Minute); err != nil {
			return err
		}
		
		// Get
		_, err := testRedis.Get(ctx, key)
		return err
	}

	framework.RunBenchmark(b, operation)
}

// Helper function for auth metadata
func addAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "$service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}
EOF

    print_success "Simple benchmark created for $service"
}

# Main execution
main() {
    echo "=================================================="
    echo "  Fixing Benchmark Issues"
    echo "=================================================="
    echo ""
    
    # Add helper methods to framework
    add_helper_methods
    
    # Create simple benchmarks for services that need fixing
    create_simple_benchmark "auth-service" "auth"
    create_simple_benchmark "product-service" "product" 
    create_simple_benchmark "order-service" "order"
    create_simple_benchmark "notification-service" "notification"
    
    print_success "All benchmark issues fixed!"
    print_status "You can now run: ./scripts/validate-benchmarks.sh"
}

main "$@"
