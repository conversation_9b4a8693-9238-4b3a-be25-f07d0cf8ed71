package benchmark

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
)

var (
	httpClient    *http.Client
	baseURL       string
	testGenerator *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupAPIGatewayBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup API Gateway benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	os.Exit(code)
}

func setupAPIGatewayBenchmarkEnvironment() error {
	httpClient = &http.Client{
		Timeout: 10 * time.Second,
	}
	baseURL = "http://localhost:8080" // API Gateway HTTP port
	testGenerator = benchmark.NewTestDataGenerator()

	return nil
}

// HTTP Endpoint Benchmarks
func BenchmarkAPIGateway_HTTP_UserRegistration(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "http_user_registration",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		userData := testGenerator.GenerateUserData()
		payload := map[string]interface{}{
			"username": userData["username"],
			"email":    userData["email"],
			"category": userData["category"],
		}

		return makeHTTPRequest(ctx, "POST", "/api/users", payload, nil)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkAPIGateway_HTTP_UserLogin(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "http_user_login",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		userData := testGenerator.GenerateUserData()
		payload := map[string]interface{}{
			"email":    userData["email"],
			"password": "testpassword123",
		}

		return makeHTTPRequest(ctx, "POST", "/api/auth/login", payload, nil)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkAPIGateway_HTTP_GetUser(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "http_get_user",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		userID := testGenerator.GenerateUserID()
		endpoint := fmt.Sprintf("/api/users/%d", userID)

		// Add authentication cookie
		cookies := []*http.Cookie{
			{
				Name:  "auth_token",
				Value: "test-jwt-token",
			},
		}

		return makeHTTPRequest(ctx, "GET", endpoint, nil, cookies)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkAPIGateway_HTTP_ListProducts(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "http_list_products",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		endpoint := "/api/products?page=1&page_size=20"
		return makeHTTPRequest(ctx, "GET", endpoint, nil, nil)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkAPIGateway_HTTP_ValidateVoucher(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "http_validate_voucher",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		voucherData := testGenerator.GenerateVoucherData()
		payload := map[string]interface{}{
			"code":        voucherData["code"],
			"user_id":     testGenerator.GenerateUserID(),
			"category_id": voucherData["category_id"],
		}

		cookies := []*http.Cookie{
			{
				Name:  "auth_token",
				Value: "test-jwt-token",
			},
		}

		return makeHTTPRequest(ctx, "POST", "/api/vouchers/validate", payload, cookies)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkAPIGateway_HTTP_CreateOrder(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "http_create_order",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		payload := map[string]interface{}{
			"user_id": testGenerator.GenerateUserID(),
			"items": []map[string]interface{}{
				{
					"product_id": testGenerator.GenerateProductID(),
					"quantity":   2,
					"price":      99.99,
				},
			},
			"voucher_code": testGenerator.GenerateVoucherData()["code"],
		}

		cookies := []*http.Cookie{
			{
				Name:  "auth_token",
				Value: "test-jwt-token",
			},
		}

		return makeHTTPRequest(ctx, "POST", "/api/orders", payload, cookies)
	}

	framework.RunBenchmark(b, operation)
}

// Authentication Benchmarks
func BenchmarkAPIGateway_Authentication_JWTValidation(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "auth_jwt_validation",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		cookies := []*http.Cookie{
			{
				Name:  "auth_token",
				Value: "test-jwt-token-" + fmt.Sprintf("%d", testGenerator.GenerateUserID()),
			},
		}

		return makeHTTPRequest(ctx, "GET", "/api/users/profile", nil, cookies)
	}

	framework.RunBenchmark(b, operation)
}

// Rate Limiting Benchmarks
func BenchmarkAPIGateway_RateLimiting(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "api-gateway",
		TestName:       "rate_limiting",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		// Make rapid requests to test rate limiting
		return makeHTTPRequest(ctx, "GET", "/api/products", nil, nil)
	}

	framework.RunBenchmark(b, operation)
}

// Concurrent Load Tests
func BenchmarkAPIGateway_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "api-gateway-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 20,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "list_products",
				Weight: 40,
				Execute: func(ctx context.Context) error {
					return makeHTTPRequest(ctx, "GET", "/api/products?page=1&page_size=20", nil, nil)
				},
			},
			{
				Name:   "get_user",
				Weight: 25,
				Execute: func(ctx context.Context) error {
					userID := testGenerator.GenerateUserID()
					endpoint := fmt.Sprintf("/api/users/%d", userID)
					cookies := []*http.Cookie{
						{Name: "auth_token", Value: "test-jwt-token"},
					}
					return makeHTTPRequest(ctx, "GET", endpoint, nil, cookies)
				},
			},
			{
				Name:   "validate_voucher",
				Weight: 20,
				Execute: func(ctx context.Context) error {
					voucherData := testGenerator.GenerateVoucherData()
					payload := map[string]interface{}{
						"code":        voucherData["code"],
						"user_id":     testGenerator.GenerateUserID(),
						"category_id": voucherData["category_id"],
					}
					cookies := []*http.Cookie{
						{Name: "auth_token", Value: "test-jwt-token"},
					}
					return makeHTTPRequest(ctx, "POST", "/api/vouchers/validate", payload, cookies)
				},
			},
			{
				Name:   "user_login",
				Weight: 10,
				Execute: func(ctx context.Context) error {
					userData := testGenerator.GenerateUserData()
					payload := map[string]interface{}{
						"email":    userData["email"],
						"password": "testpassword123",
					}
					return makeHTTPRequest(ctx, "POST", "/api/auth/login", payload, nil)
				},
			},
			{
				Name:   "create_order",
				Weight: 5,
				Execute: func(ctx context.Context) error {
					payload := map[string]interface{}{
						"user_id": testGenerator.GenerateUserID(),
						"items": []map[string]interface{}{
							{
								"product_id": testGenerator.GenerateProductID(),
								"quantity":   1,
								"price":      49.99,
							},
						},
					}
					cookies := []*http.Cookie{
						{Name: "auth_token", Value: "test-jwt-token"},
					}
					return makeHTTPRequest(ctx, "POST", "/api/orders", payload, cookies)
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

// Stress Test - High Concurrency
func BenchmarkAPIGateway_StressTest(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "api-gateway-stress-test",
		Duration:    60 * time.Second,
		Concurrency: 50,
		RampUpTime:  10 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "high_load_products",
				Weight: 60,
				Execute: func(ctx context.Context) error {
					return makeHTTPRequest(ctx, "GET", "/api/products", nil, nil)
				},
			},
			{
				Name:   "high_load_vouchers",
				Weight: 40,
				Execute: func(ctx context.Context) error {
					voucherData := testGenerator.GenerateVoucherData()
					payload := map[string]interface{}{
						"code":        voucherData["code"],
						"user_id":     testGenerator.GenerateUserID(),
						"category_id": voucherData["category_id"],
					}
					cookies := []*http.Cookie{
						{Name: "auth_token", Value: "test-jwt-token"},
					}
					return makeHTTPRequest(ctx, "POST", "/api/vouchers/validate", payload, cookies)
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

// Helper functions
func makeHTTPRequest(ctx context.Context, method, endpoint string, payload interface{}, cookies []*http.Cookie) error {
	var body bytes.Buffer
	if payload != nil {
		if err := json.NewEncoder(&body).Encode(payload); err != nil {
			return fmt.Errorf("failed to encode payload: %w", err)
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, baseURL+endpoint, &body)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	if payload != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Add cookies
	for _, cookie := range cookies {
		req.AddCookie(cookie)
	}

	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("HTTP error: %d", resp.StatusCode)
	}

	return nil
}

// Helper functions for generating test data
func generateAPIGatewayUserID() int {
	return testGenerator.Rand().Intn(1000) + 1
}
