#!/bin/bash

# Benchmark Validation Script
# This script validates that all benchmark implementations are complete and correct

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICES=("auth-service" "user-service" "voucher-service" "product-service" "order-service" "notification-service" "api-gateway")

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNING_CHECKS++))
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

# Function to run a check
run_check() {
    local check_name="$1"
    local check_command="$2"
    
    ((TOTAL_CHECKS++))
    
    if eval "$check_command" >/dev/null 2>&1; then
        print_success "$check_name"
        return 0
    else
        print_error "$check_name"
        return 1
    fi
}

# Function to run a check with warning
run_check_warn() {
    local check_name="$1"
    local check_command="$2"
    
    ((TOTAL_CHECKS++))
    
    if eval "$check_command" >/dev/null 2>&1; then
        print_success "$check_name"
        return 0
    else
        print_warning "$check_name"
        return 1
    fi
}

# Function to check benchmark file structure
check_benchmark_structure() {
    echo "=================================================="
    echo "  Benchmark File Structure Validation"
    echo "=================================================="
    
    for service in "${SERVICES[@]}"; do
        local service_dir="$PROJECT_ROOT/coupon-$service"
        
        print_status "Checking $service..."
        
        # Check if service directory exists
        run_check "$service directory exists" "test -d '$service_dir'"
        
        # Check if benchmark directory exists
        run_check "$service benchmark directory exists" "test -d '$service_dir/benchmark'"
        
        # Check if benchmark test file exists
        run_check "$service benchmark test file exists" "test -f '$service_dir/benchmark/${service}_benchmark_test.go'"
        
        # Check if go.mod exists in service
        run_check_warn "$service go.mod exists" "test -f '$service_dir/go.mod'"
        
        echo ""
    done
}

# Function to check benchmark test content
check_benchmark_content() {
    echo "=================================================="
    echo "  Benchmark Test Content Validation"
    echo "=================================================="
    
    for service in "${SERVICES[@]}"; do
        local service_dir="$PROJECT_ROOT/coupon-$service"
        local benchmark_file="$service_dir/benchmark/${service}_benchmark_test.go"
        
        print_status "Checking $service benchmark content..."
        
        if [ -f "$benchmark_file" ]; then
            # Check for required benchmark functions
            run_check "$service has gRPC benchmarks" "grep -q 'BenchmarkGRPC' '$benchmark_file'"
            run_check "$service has database benchmarks" "grep -q 'BenchmarkDatabase' '$benchmark_file'"
            run_check "$service has cache benchmarks" "grep -q 'BenchmarkCache' '$benchmark_file'"
            run_check "$service has concurrent load tests" "grep -q 'BenchmarkConcurrentLoad' '$benchmark_file'"
            
            # Check for proper imports
            run_check "$service imports benchmark framework" "grep -q 'coupon-shared-libs/benchmark' '$benchmark_file'"
            run_check "$service imports testing package" "grep -q '\"testing\"' '$benchmark_file'"
            run_check "$service imports context package" "grep -q '\"context\"' '$benchmark_file'"
            
            # Check for TestMain function
            run_check "$service has TestMain function" "grep -q 'func TestMain' '$benchmark_file'"
            
            # Check for cleanup function
            run_check "$service has cleanup function" "grep -q 'cleanup' '$benchmark_file'"
            
        else
            print_error "$service benchmark file not found"
            ((FAILED_CHECKS += 5)) # Account for skipped checks
            ((TOTAL_CHECKS += 5))
        fi
        
        echo ""
    done
}

# Function to check Go syntax
check_go_syntax() {
    echo "=================================================="
    echo "  Go Syntax Validation"
    echo "=================================================="
    
    for service in "${SERVICES[@]}"; do
        local service_dir="$PROJECT_ROOT/coupon-$service"
        local benchmark_file="$service_dir/benchmark/${service}_benchmark_test.go"
        
        print_status "Checking $service Go syntax..."
        
        if [ -f "$benchmark_file" ]; then
            cd "$service_dir"
            
            # Check Go syntax
            run_check_warn "$service Go syntax is valid" "go vet ./benchmark/..."
            
            # Check if benchmark compiles
            run_check_warn "$service benchmark compiles" "go test -c ./benchmark/..."
            
            # Clean up compiled test binary
            rm -f benchmark.test 2>/dev/null || true
            
            cd "$PROJECT_ROOT"
        else
            print_warning "$service benchmark file not found - skipping syntax check"
            ((WARNING_CHECKS++))
            ((TOTAL_CHECKS++))
        fi
        
        echo ""
    done
}

# Function to check dependencies
check_dependencies() {
    echo "=================================================="
    echo "  Dependency Validation"
    echo "=================================================="
    
    # Check shared libraries
    run_check "Shared benchmark library exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/benchmark/framework.go'"
    run_check "Shared metrics library exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/metrics/metrics.go'"
    run_check "Shared config library exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/config/config.go'"
    run_check "Shared database library exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/database/postgres.go'"
    run_check "Shared redis library exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/redis/redis.go'"
    
    # Check proto files
    run_check_warn "Proto files directory exists" "test -d '$PROJECT_ROOT/coupon-proto'"
    
    echo ""
}

# Function to check benchmark patterns
check_benchmark_patterns() {
    echo "=================================================="
    echo "  Benchmark Pattern Validation"
    echo "=================================================="
    
    for service in "${SERVICES[@]}"; do
        local service_dir="$PROJECT_ROOT/coupon-$service"
        local benchmark_file="$service_dir/benchmark/${service}_benchmark_test.go"
        
        print_status "Checking $service benchmark patterns..."
        
        if [ -f "$benchmark_file" ]; then
            # Check for proper benchmark function naming
            run_check "$service has properly named benchmark functions" "grep -q '^func Benchmark.*_gRPC_' '$benchmark_file'"
            
            # Check for benchmark framework usage
            run_check "$service uses benchmark framework" "grep -q 'benchmark.NewBenchmarkFramework' '$benchmark_file'"
            
            # Check for gRPC suite usage
            run_check "$service uses gRPC benchmark suite" "grep -q 'benchmark.NewGRPCBenchmarkSuite' '$benchmark_file'"
            
            # Check for database suite usage
            run_check "$service uses database benchmark suite" "grep -q 'benchmark.NewDatabaseBenchmarkSuite' '$benchmark_file'"
            
            # Check for load test scenarios
            run_check "$service has load test scenarios" "grep -q 'benchmark.LoadTestScenario' '$benchmark_file'"
            
            # Check for authentication metadata
            run_check "$service adds auth metadata" "grep -q 'AuthMetadata' '$benchmark_file'"
            
        else
            print_error "$service benchmark file not found"
            ((FAILED_CHECKS += 6))
            ((TOTAL_CHECKS += 6))
        fi
        
        echo ""
    done
}

# Function to check benchmark runner
check_benchmark_runner() {
    echo "=================================================="
    echo "  Benchmark Runner Validation"
    echo "=================================================="
    
    run_check "Benchmark runner script exists" "test -f '$PROJECT_ROOT/scripts/run-benchmarks.sh'"
    run_check "Benchmark runner is executable" "test -x '$PROJECT_ROOT/scripts/run-benchmarks.sh'"
    run_check "Makefile.monitoring exists" "test -f '$PROJECT_ROOT/Makefile.monitoring'"
    
    # Check if runner script includes all services
    if [ -f "$PROJECT_ROOT/scripts/run-benchmarks.sh" ]; then
        for service in "${SERVICES[@]}"; do
            run_check_warn "Runner includes $service" "grep -q '$service' '$PROJECT_ROOT/scripts/run-benchmarks.sh'"
        done
    fi
    
    echo ""
}

# Function to generate validation report
generate_report() {
    local report_file="$PROJECT_ROOT/benchmark-validation-report.md"
    
    print_status "Generating validation report..."
    
    cat > "$report_file" << EOF
# Benchmark Validation Report

**Generated:** $(date)

## Summary

- **Total Checks:** $TOTAL_CHECKS
- **Passed:** $PASSED_CHECKS
- **Warnings:** $WARNING_CHECKS
- **Failed:** $FAILED_CHECKS

## Service Status

| Service | Benchmark File | Structure | Content | Syntax |
|---------|---------------|-----------|---------|--------|
EOF

    for service in "${SERVICES[@]}"; do
        local service_dir="$PROJECT_ROOT/coupon-$service"
        local benchmark_file="$service_dir/benchmark/${service}_benchmark_test.go"
        
        local file_status="❌"
        local structure_status="❌"
        local content_status="❌"
        local syntax_status="❌"
        
        if [ -f "$benchmark_file" ]; then
            file_status="✅"
            
            if [ -d "$service_dir/benchmark" ]; then
                structure_status="✅"
            fi
            
            if grep -q "BenchmarkGRPC" "$benchmark_file" 2>/dev/null; then
                content_status="✅"
            fi
            
            cd "$service_dir" 2>/dev/null && go vet ./benchmark/... >/dev/null 2>&1 && syntax_status="✅"
            cd "$PROJECT_ROOT"
        fi
        
        echo "| $service | $file_status | $structure_status | $content_status | $syntax_status |" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

## Recommendations

EOF

    if [ $FAILED_CHECKS -gt 0 ]; then
        echo "### Critical Issues" >> "$report_file"
        echo "- $FAILED_CHECKS critical issues found" >> "$report_file"
        echo "- Review failed checks above and fix before running benchmarks" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    if [ $WARNING_CHECKS -gt 0 ]; then
        echo "### Warnings" >> "$report_file"
        echo "- $WARNING_CHECKS warnings found" >> "$report_file"
        echo "- These may affect benchmark accuracy but won't prevent execution" >> "$report_file"
        echo "" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF
### Next Steps

1. **Fix Critical Issues** - Address all failed checks
2. **Review Warnings** - Consider addressing warnings for better results
3. **Test Compilation** - Run \`go test -c\` in each service benchmark directory
4. **Run Benchmarks** - Use \`./scripts/run-benchmarks.sh\` to execute tests
5. **Monitor Results** - Check benchmark results for performance insights

---
*Generated by Benchmark Validation Script*
EOF

    print_success "Validation report generated: $report_file"
}

# Summary function
print_summary() {
    echo "=================================================="
    echo "  Validation Summary"
    echo "=================================================="
    echo ""
    echo "Total Checks: $TOTAL_CHECKS"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Warnings: ${YELLOW}$WARNING_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        if [ $WARNING_CHECKS -eq 0 ]; then
            echo -e "${GREEN}✓ All benchmark validations passed!${NC}"
            echo "Benchmarks are ready to run."
        else
            echo -e "${YELLOW}⚠ Benchmarks are mostly ready with some warnings.${NC}"
            echo "Consider addressing warnings for optimal results."
        fi
    else
        echo -e "${RED}✗ Benchmark validation failed.${NC}"
        echo "Please address the failed checks before running benchmarks."
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "  Coupon System - Benchmark Validation"
    echo "=================================================="
    echo ""
    
    check_benchmark_structure
    check_benchmark_content
    check_go_syntax
    check_dependencies
    check_benchmark_patterns
    check_benchmark_runner
    generate_report
    print_summary
}

# Handle script arguments
case "${1:-}" in
    "structure")
        check_benchmark_structure
        ;;
    "content")
        check_benchmark_content
        ;;
    "syntax")
        check_go_syntax
        ;;
    "dependencies")
        check_dependencies
        ;;
    "patterns")
        check_benchmark_patterns
        ;;
    "runner")
        check_benchmark_runner
        ;;
    *)
        main
        ;;
esac
