#!/bin/bash

# Coupon System - Monitoring and Benchmarking Deployment Script
# This script sets up Prometheus, Grafana, and monitoring infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MONITORING_DIR="$PROJECT_ROOT/monitoring"
NETWORK_NAME="coupon-network"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to create Docker network
create_network() {
    print_status "Creating Docker network: $NETWORK_NAME"
    
    if docker network ls | grep -q "$NETWORK_NAME"; then
        print_warning "Network $NETWORK_NAME already exists"
    else
        docker network create "$NETWORK_NAME"
        print_success "Network $NETWORK_NAME created"
    fi
}

# Function to create monitoring directories
create_directories() {
    print_status "Creating monitoring directories..."
    
    mkdir -p "$MONITORING_DIR/prometheus/data"
    mkdir -p "$MONITORING_DIR/grafana/data"
    mkdir -p "$MONITORING_DIR/alertmanager/data"
    
    # Set proper permissions for Grafana
    sudo chown -R 472:472 "$MONITORING_DIR/grafana/data" 2>/dev/null || true
    
    print_success "Monitoring directories created"
}

# Function to validate configuration files
validate_configs() {
    print_status "Validating configuration files..."
    
    # Check Prometheus config
    if [ ! -f "$MONITORING_DIR/prometheus/prometheus.yml" ]; then
        print_error "Prometheus configuration file not found: $MONITORING_DIR/prometheus/prometheus.yml"
        exit 1
    fi
    
    # Check AlertManager config
    if [ ! -f "$MONITORING_DIR/alertmanager/alertmanager.yml" ]; then
        print_error "AlertManager configuration file not found: $MONITORING_DIR/alertmanager/alertmanager.yml"
        exit 1
    fi
    
    # Check Grafana provisioning
    if [ ! -d "$MONITORING_DIR/grafana/provisioning" ]; then
        print_error "Grafana provisioning directory not found: $MONITORING_DIR/grafana/provisioning"
        exit 1
    fi
    
    print_success "Configuration files validated"
}

# Function to start monitoring stack
start_monitoring() {
    print_status "Starting monitoring stack..."
    
    cd "$PROJECT_ROOT"
    
    # Start monitoring services
    docker-compose -f docker-compose.monitoring.yml up -d
    
    print_success "Monitoring stack started"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for Prometheus
    print_status "Waiting for Prometheus..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:9090/-/ready >/dev/null 2>&1; then
            print_success "Prometheus is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Prometheus failed to start within timeout"
        exit 1
    fi
    
    # Wait for Grafana
    print_status "Waiting for Grafana..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:3000/api/health >/dev/null 2>&1; then
            print_success "Grafana is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Grafana failed to start within timeout"
        exit 1
    fi
    
    # Wait for AlertManager
    print_status "Waiting for AlertManager..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:9093/-/ready >/dev/null 2>&1; then
            print_success "AlertManager is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "AlertManager failed to start within timeout"
        exit 1
    fi
}

# Function to display service URLs
display_urls() {
    print_success "Monitoring stack deployed successfully!"
    echo ""
    echo "Service URLs:"
    echo "  Grafana:      http://localhost:3000 (admin/admin123)"
    echo "  Prometheus:   http://localhost:9090"
    echo "  AlertManager: http://localhost:9093"
    echo "  Node Exporter: http://localhost:9100"
    echo "  cAdvisor:     http://localhost:8080"
    echo ""
    echo "Default Grafana credentials:"
    echo "  Username: admin"
    echo "  Password: admin123"
    echo ""
    echo "To view logs: docker-compose -f docker-compose.monitoring.yml logs -f [service]"
    echo "To stop: docker-compose -f docker-compose.monitoring.yml down"
}

# Function to run health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # Check Prometheus targets
    print_status "Checking Prometheus targets..."
    targets_response=$(curl -s http://localhost:9090/api/v1/targets)
    if echo "$targets_response" | grep -q '"health":"up"'; then
        print_success "Some Prometheus targets are healthy"
    else
        print_warning "No healthy Prometheus targets found. Services may not be running."
    fi
    
    # Check Grafana datasources
    print_status "Checking Grafana datasources..."
    if curl -s -u admin:admin123 http://localhost:3000/api/datasources | grep -q "Prometheus"; then
        print_success "Grafana Prometheus datasource configured"
    else
        print_warning "Grafana Prometheus datasource not found"
    fi
}

# Function to setup benchmark environment
setup_benchmark_env() {
    print_status "Setting up benchmark environment..."
    
    # Create benchmark results directory
    mkdir -p "$PROJECT_ROOT/benchmark-results"
    
    # Create benchmark runner script
    cat > "$PROJECT_ROOT/scripts/run-benchmarks.sh" << 'EOF'
#!/bin/bash

# Benchmark runner script
set -e

SERVICES=("user-service" "voucher-service" "product-service" "auth-service" "api-gateway" "order-service" "notification-service")
RESULTS_DIR="$(dirname "$0")/../benchmark-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "Starting benchmark run at $TIMESTAMP"

for service in "${SERVICES[@]}"; do
    echo "Running benchmarks for $service..."
    
    if [ -d "coupon-$service/benchmark" ]; then
        cd "coupon-$service"
        go test -bench=. -benchmem ./benchmark/... > "$RESULTS_DIR/${service}_benchmark_${TIMESTAMP}.txt" 2>&1
        cd ..
        echo "Benchmark completed for $service"
    else
        echo "No benchmark directory found for $service"
    fi
done

echo "All benchmarks completed. Results saved in $RESULTS_DIR"
EOF
    
    chmod +x "$PROJECT_ROOT/scripts/run-benchmarks.sh"
    
    print_success "Benchmark environment setup completed"
}

# Main execution
main() {
    echo "=================================================="
    echo "  Coupon System - Monitoring Deployment Script"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    create_network
    create_directories
    validate_configs
    start_monitoring
    wait_for_services
    run_health_checks
    setup_benchmark_env
    display_urls
    
    echo ""
    print_success "Deployment completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "start")
        start_monitoring
        wait_for_services
        display_urls
        ;;
    "stop")
        print_status "Stopping monitoring stack..."
        cd "$PROJECT_ROOT"
        docker-compose -f docker-compose.monitoring.yml down
        print_success "Monitoring stack stopped"
        ;;
    "restart")
        print_status "Restarting monitoring stack..."
        cd "$PROJECT_ROOT"
        docker-compose -f docker-compose.monitoring.yml down
        docker-compose -f docker-compose.monitoring.yml up -d
        wait_for_services
        display_urls
        ;;
    "status")
        print_status "Checking monitoring stack status..."
        cd "$PROJECT_ROOT"
        docker-compose -f docker-compose.monitoring.yml ps
        ;;
    "logs")
        cd "$PROJECT_ROOT"
        docker-compose -f docker-compose.monitoring.yml logs -f "${2:-}"
        ;;
    "health")
        run_health_checks
        ;;
    *)
        main
        ;;
esac
