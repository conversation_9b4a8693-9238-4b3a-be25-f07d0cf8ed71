# Makefile.monitoring GitLab Integration

## Overview

The `Makefile.monitoring` has been updated to work seamlessly with the new GitLab-integrated benchmark system. All redundant script generation has been removed, and the Makefile now properly leverages the enhanced `run-benchmarks.sh` script.

## Changes Made

### 1. Removed Script Redundancy
- **Eliminated duplicate script generation** in `deploy-monitoring.sh`
- **Removed conflicting benchmark runner creation** that overwrote the GitLab-integrated script
- **Updated deployment script** to check for existing enhanced script instead of generating a basic one

### 2. Updated Benchmark Targets
All individual service benchmark targets now use the enhanced script:

```makefile
benchmark-user: ## Run benchmarks for user service
	@USER_SERVICE_ONLY=true ./scripts/run-benchmarks.sh

benchmark-auth: ## Run benchmarks for auth service  
	@AUTH_SERVICE_ONLY=true ./scripts/run-benchmarks.sh
```

### 3. Enhanced Load/Stress Testing
Load and stress testing now use the script's built-in modes:

```makefile
benchmark-load: ## Run load testing scenarios
	@./scripts/run-benchmarks.sh load

benchmark-stress: ## Run stress testing scenarios
	@./scripts/run-benchmarks.sh stress
```

### 4. New GitLab-Specific Targets

#### `benchmark-gitlab`
Run benchmarks with GitLab integration enabled:
```bash
make benchmark-gitlab
```

#### `benchmark-branch`
Run benchmarks on a specific branch:
```bash
make benchmark-branch BRANCH=feature/performance-fix
```

#### `**********************`
Setup GitLab configuration interactively:
```bash
make **********************
```

## Usage Examples

### Local Repository Benchmarks
```bash
# Run all benchmarks (works with local repos)
make benchmark-all

# Run specific service benchmarks
make benchmark-user
make benchmark-auth
make benchmark-product

# Run load/stress tests
make benchmark-load
make benchmark-stress
```

### GitLab Integration Benchmarks
```bash
# Setup GitLab configuration (one-time)
make **********************

# Run benchmarks with GitLab auto-pull
make benchmark-gitlab

# Test specific branch
make benchmark-branch BRANCH=develop

# CI/CD friendly benchmarks
make ci-benchmark
```

### Monitoring Integration
```bash
# Deploy monitoring stack (preserves enhanced benchmark script)
make monitoring-deploy

# Generate benchmark reports
make benchmark-report

# Establish performance baselines
make performance-baseline
```

## Environment Variable Support

The Makefile now supports all GitLab integration environment variables:

### Git Configuration
```bash
# Enable GitLab integration
GIT_AUTO_PULL=true make benchmark-all

# Use specific branch
GIT_BRANCH=develop make benchmark-all

# Use custom base directory
GIT_BASE_DIR=/tmp/my-repos make benchmark-all
```

### Service Selection
```bash
# Run only specific services
AUTH_SERVICE_ONLY=true make benchmark-all
USER_SERVICE_ONLY=true make benchmark-all
```

### Authentication
```bash
# Use GitLab credentials
GIT_USERNAME=myuser GIT_TOKEN=mytoken make benchmark-gitlab
```

## Compatibility

### Backward Compatibility
- All existing Makefile targets continue to work
- Local repository setups work without changes
- Existing CI/CD pipelines remain functional

### Forward Compatibility
- New GitLab features are automatically available
- Configuration-driven approach allows easy updates
- Extensible for future enhancements

## Integration with CI/CD

### GitLab CI Integration
```yaml
# .gitlab-ci.yml
benchmark:
  stage: test
  script:
    - make benchmark-gitlab
  variables:
    GIT_AUTO_PULL: "true"
    GIT_BRANCH: "$CI_COMMIT_REF_NAME"
```

### Jenkins Integration
```groovy
pipeline {
    stages {
        stage('Benchmark') {
            steps {
                sh 'make benchmark-gitlab'
            }
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **Script Not Found**
   ```
   Error: scripts/run-benchmarks.sh not found
   ```
   **Solution**: Ensure the enhanced benchmark script exists and is executable

2. **Permission Denied**
   ```
   Error: Permission denied: scripts/run-benchmarks.sh
   ```
   **Solution**: The Makefile automatically sets execute permissions

3. **GitLab Authentication Failed**
   ```
   Error: Failed to clone repository
   ```
   **Solution**: Run `make **********************` to configure authentication

4. **Service Not Found**
   ```
   Warning: No benchmark directory found for service
   ```
   **Solution**: Check GitLab configuration or local repository paths

### Debug Mode
Enable verbose output:
```bash
# Debug Makefile execution
make benchmark-all VERBOSE=1

# Debug Git operations
GIT_TRACE=1 make benchmark-gitlab
```

## Migration Guide

### From Old System
If you were using the old benchmark system:

1. **Remove old configurations**: Delete any manually created benchmark scripts
2. **Update CI/CD**: Replace direct `go test` commands with Makefile targets
3. **Configure GitLab**: Run `make **********************` for GitLab integration

### Configuration Migration
Old hardcoded paths are now configurable:
```bash
# Old way (hardcoded)
cd coupon-user-service && go test -bench=. ./benchmark/

# New way (configurable)
make benchmark-user
```

## Best Practices

### Development Workflow
```bash
# 1. Setup GitLab integration (one-time)
make **********************

# 2. Run quick tests during development
make ci-benchmark

# 3. Run full benchmarks before commits
make benchmark-all

# 4. Test specific branches
make benchmark-branch BRANCH=feature/my-feature
```

### Production Workflow
```bash
# 1. Establish baselines
make performance-baseline

# 2. Run comprehensive tests
make benchmark-load
make benchmark-stress

# 3. Generate reports
make benchmark-report

# 4. Compare with baselines
make benchmark-compare BASELINE=baseline.txt CURRENT=current.txt
```

## Summary

The updated `Makefile.monitoring` provides:
- ✅ **Clean Integration**: No script conflicts or redundancy
- ✅ **GitLab Support**: Full multi-repository capabilities
- ✅ **Backward Compatibility**: Existing workflows continue to work
- ✅ **Enhanced Features**: New GitLab-specific targets and options
- ✅ **Simplified Usage**: Single command for complex operations
- ✅ **CI/CD Ready**: Perfect for automated testing pipelines

The monitoring system now works seamlessly with the GitLab multi-repository benchmark setup while maintaining all existing functionality.
