# GitLab Multi-Repository Benchmark Runner

This guide explains how to use the benchmark runner with GitLab-hosted microservice repositories. The enhanced script can automatically clone or update repositories from GitLab before running benchmarks.

## Quick Start

### 1. Automated Setup (Recommended)
Use the GitLab setup script for guided configuration:

```bash
./scripts/setup-gitlab-benchmarks.sh
```

This script will:
- Collect your GitLab group/username
- Configure authentication method
- Set up repository URLs
- Test repository access
- Create the configuration file

### 2. Manual Setup
Copy and customize the GitLab configuration template:

```bash
cp scripts/benchmark-config-gitlab.env.example scripts/benchmark-config.env
# Edit the file with your GitLab details
```

### 3. Run Benchmarks
Once configured, run benchmarks normally:

```bash
./scripts/run-benchmarks.sh
```

The script will automatically:
1. Clone repositories (first run) or pull latest changes
2. Switch to the specified branch
3. Run benchmarks on each service
4. Generate reports

## Configuration

### Basic Configuration
```bash
# Enable Git auto-pull
GIT_AUTO_PULL="true"

# Directory for cloned repositories
GIT_BASE_DIR="/tmp/benchmark-repos"

# Branch to test
GIT_BRANCH="main"
```

### Authentication Methods

#### Public Repositories
For public repositories, no authentication is needed:
```bash
AUTH_SERVICE_GIT_URL="https://gitlab.com/your-group/auth-service.git"
```

#### Private Repositories with Personal Access Token
1. Create a Personal Access Token in GitLab:
   - Go to GitLab → Settings → Access Tokens
   - Create token with `read_repository` scope
   - Copy the token

2. Configure authentication:
```bash
GIT_USERNAME="your-gitlab-username"
GIT_TOKEN="glpat-xxxxxxxxxxxxxxxxxxxx"
AUTH_SERVICE_GIT_URL="https://gitlab.com/your-group/auth-service.git"
```

#### SSH Key Authentication
1. Set up SSH keys in GitLab:
   - Generate SSH key: `ssh-keygen -t ed25519 -C "<EMAIL>"`
   - Add public key to GitLab → Settings → SSH Keys
   - Test connection: `ssh -T **************`

2. Use SSH URLs:
```bash
AUTH_SERVICE_GIT_URL="**************:your-group/auth-service.git"
```

### Repository URL Configuration
Configure URLs for all your services:

```bash
# Replace 'your-group' with your actual GitLab group/username
AUTH_SERVICE_GIT_URL="https://gitlab.com/your-group/auth-service.git"
USER_SERVICE_GIT_URL="https://gitlab.com/your-group/user-service.git"
VOUCHER_SERVICE_GIT_URL="https://gitlab.com/your-group/voucher-service.git"
PRODUCT_SERVICE_GIT_URL="https://gitlab.com/your-group/product-service.git"
ORDER_SERVICE_GIT_URL="https://gitlab.com/your-group/order-service.git"
NOTIFICATION_SERVICE_GIT_URL="https://gitlab.com/your-group/notification-service.git"
API_GATEWAY_GIT_URL="https://gitlab.com/your-group/api-gateway.git"
```

## Usage Examples

### Basic Usage
```bash
# Run benchmarks with Git auto-pull
./scripts/run-benchmarks.sh

# Quick benchmarks (10s duration)
./scripts/run-benchmarks.sh quick

# Load testing (60s duration, 20 concurrency)
./scripts/run-benchmarks.sh load
```

### Custom Branch Testing
```bash
# Test a specific branch
GIT_BRANCH="develop" ./scripts/run-benchmarks.sh

# Test feature branch
GIT_BRANCH="feature/performance-improvements" ./scripts/run-benchmarks.sh
```

### Custom Base Directory
```bash
# Use custom directory for repositories
GIT_BASE_DIR="/home/<USER>/benchmark-repos" ./scripts/run-benchmarks.sh
```

### Disable Git Operations
```bash
# Run benchmarks without Git operations (use existing local repos)
GIT_AUTO_PULL="false" ./scripts/run-benchmarks.sh
```

## GitLab CI/CD Integration

### Pipeline Configuration
Add to your `.gitlab-ci.yml`:

```yaml
benchmark:
  stage: test
  image: golang:1.21
  variables:
    GIT_AUTO_PULL: "true"
    GIT_BASE_DIR: "/tmp/benchmark-repos"
    GIT_BRANCH: "$CI_COMMIT_REF_NAME"
    GIT_USERNAME: "$CI_REGISTRY_USER"
    GIT_TOKEN: "$CI_REGISTRY_PASSWORD"
  script:
    - ./scripts/run-benchmarks.sh quick
  artifacts:
    reports:
      junit: benchmark-results/*.xml
    paths:
      - benchmark-results/
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests
```

### Environment Variables in GitLab
Set these in GitLab → Settings → CI/CD → Variables:

- `GITLAB_BENCHMARK_TOKEN`: Personal Access Token for repository access
- `BENCHMARK_DURATION`: Default benchmark duration
- `BENCHMARK_CONCURRENCY`: Default concurrency level

## Directory Structure

When Git auto-pull is enabled, the script creates this structure:

```
/tmp/benchmark-repos/          # GIT_BASE_DIR
├── auth-service/              # Cloned repository
│   ├── benchmark/
│   │   └── *_test.go
│   ├── go.mod
│   └── ...
├── user-service/              # Cloned repository
│   ├── benchmark/
│   └── ...
└── ...
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   ```
   [ERROR] Failed to clone repository
   ```
   - Verify GitLab username and token
   - Ensure token has `read_repository` scope
   - Test access: `git ls-remote https://username:<EMAIL>/group/repo.git`

2. **Repository Not Found**
   ```
   [ERROR] Repository not found
   ```
   - Check GitLab URLs are correct
   - Verify you have access to repositories
   - Ensure group/project names are spelled correctly

3. **SSH Authentication Issues**
   ```
   [ERROR] Permission denied (publickey)
   ```
   - Test SSH connection: `ssh -T **************`
   - Verify SSH keys are added to GitLab
   - Use HTTPS URLs with token instead

4. **Branch Not Found**
   ```
   [ERROR] Failed to checkout branch
   ```
   - Verify branch exists in all repositories
   - Use `main` or `master` as appropriate
   - Check branch name spelling

5. **Permission Denied on Directory**
   ```
   [ERROR] Permission denied: /tmp/benchmark-repos
   ```
   - Ensure GIT_BASE_DIR is writable
   - Try different directory: `GIT_BASE_DIR="$HOME/benchmark-repos"`

### Debug Mode
Enable verbose Git output:
```bash
GIT_TRACE=1 ./scripts/run-benchmarks.sh
```

### Manual Repository Testing
Test individual repository access:
```bash
# Test HTTPS access
git ls-remote https://gitlab.com/your-group/auth-service.git

# Test SSH access
git ls-remote **************:your-group/auth-service.git

# Test with credentials
git ls-remote https://username:<EMAIL>/your-group/auth-service.git
```

## Security Considerations

1. **Personal Access Tokens**
   - Use tokens with minimal required scopes (`read_repository`)
   - Set expiration dates on tokens
   - Store tokens securely (environment variables, not in code)

2. **SSH Keys**
   - Use Ed25519 keys for better security
   - Protect private keys with passphrases
   - Regularly rotate SSH keys

3. **Configuration Files**
   - Don't commit configuration files with credentials
   - Add `benchmark-config.env` to `.gitignore`
   - Use environment variables in CI/CD

## Performance Tips

1. **Repository Caching**
   - Use persistent directories instead of `/tmp` for better performance
   - Consider using `GIT_BASE_DIR="$HOME/.benchmark-cache"`

2. **Parallel Operations**
   - The script updates repositories sequentially
   - For faster updates, consider running updates in parallel

3. **Shallow Clones**
   - For large repositories, consider shallow clones
   - Modify the script to use `git clone --depth 1`

## Support

For issues specific to GitLab integration:
1. Check GitLab repository permissions
2. Verify network connectivity to GitLab
3. Test Git operations manually
4. Review GitLab access logs
5. Check GitLab service status
