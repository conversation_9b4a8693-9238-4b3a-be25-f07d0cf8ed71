#!/bin/bash

# Coupon System - Monitoring Setup Validation Script
# This script validates the monitoring and benchmarking setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
MONITORING_DIR="$PROJECT_ROOT/monitoring"

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNING_CHECKS++))
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

# Function to run a check
run_check() {
    local check_name="$1"
    local check_command="$2"
    
    ((TOTAL_CHECKS++))
    print_status "Checking: $check_name"
    
    if eval "$check_command" >/dev/null 2>&1; then
        print_success "$check_name"
        return 0
    else
        print_error "$check_name"
        return 1
    fi
}

# Function to run a check with warning
run_check_warn() {
    local check_name="$1"
    local check_command="$2"
    
    ((TOTAL_CHECKS++))
    print_status "Checking: $check_name"
    
    if eval "$check_command" >/dev/null 2>&1; then
        print_success "$check_name"
        return 0
    else
        print_warning "$check_name"
        return 1
    fi
}

# Prerequisites checks
check_prerequisites() {
    echo "=================================================="
    echo "  Prerequisites Validation"
    echo "=================================================="
    
    run_check "Docker installed" "command -v docker"
    run_check "Docker Compose installed" "command -v docker-compose"
    run_check "Docker daemon running" "docker info"
    run_check "Go installed" "command -v go"
    run_check "Make installed" "command -v make"
    run_check "Curl installed" "command -v curl"
    
    echo ""
}

# File structure checks
check_file_structure() {
    echo "=================================================="
    echo "  File Structure Validation"
    echo "=================================================="
    
    # Core files
    run_check "Docker Compose monitoring file exists" "test -f '$PROJECT_ROOT/docker-compose.monitoring.yml'"
    run_check "Deployment script exists" "test -f '$PROJECT_ROOT/scripts/deploy-monitoring.sh'"
    run_check "Monitoring Makefile exists" "test -f '$PROJECT_ROOT/Makefile.monitoring'"
    
    # Prometheus files
    run_check "Prometheus config exists" "test -f '$MONITORING_DIR/prometheus/prometheus.yml'"
    run_check "Prometheus rules directory exists" "test -d '$MONITORING_DIR/prometheus/rules'"
    run_check "Service alerts rules exist" "test -f '$MONITORING_DIR/prometheus/rules/service_alerts.yml'"
    run_check "Security alerts rules exist" "test -f '$MONITORING_DIR/prometheus/rules/security_alerts.yml'"
    
    # Grafana files
    run_check "Grafana provisioning directory exists" "test -d '$MONITORING_DIR/grafana/provisioning'"
    run_check "Grafana datasources config exists" "test -f '$MONITORING_DIR/grafana/provisioning/datasources/prometheus.yml'"
    run_check "Grafana dashboards config exists" "test -f '$MONITORING_DIR/grafana/provisioning/dashboards/dashboards.yml'"
    run_check "Grafana dashboards directory exists" "test -d '$MONITORING_DIR/grafana/dashboards'"
    
    # AlertManager files
    run_check "AlertManager config exists" "test -f '$MONITORING_DIR/alertmanager/alertmanager.yml'"
    
    # Documentation
    run_check "Monitoring README exists" "test -f '$PROJECT_ROOT/MONITORING_README.md'"
    run_check "Benchmark guide exists" "test -f '$PROJECT_ROOT/BENCHMARK_GUIDE.md'"
    
    echo ""
}

# Configuration validation
check_configurations() {
    echo "=================================================="
    echo "  Configuration Validation"
    echo "=================================================="
    
    # Validate Prometheus config
    if command -v promtool >/dev/null 2>&1; then
        run_check "Prometheus config syntax" "promtool check config '$MONITORING_DIR/prometheus/prometheus.yml'"
        run_check "Prometheus rules syntax" "promtool check rules '$MONITORING_DIR/prometheus/rules/'*.yml"
    else
        print_warning "promtool not available - skipping Prometheus config validation"
        ((WARNING_CHECKS++))
    fi
    
    # Validate YAML files
    if command -v yamllint >/dev/null 2>&1; then
        run_check_warn "Prometheus YAML syntax" "yamllint '$MONITORING_DIR/prometheus/prometheus.yml'"
        run_check_warn "AlertManager YAML syntax" "yamllint '$MONITORING_DIR/alertmanager/alertmanager.yml'"
        run_check_warn "Grafana datasource YAML syntax" "yamllint '$MONITORING_DIR/grafana/provisioning/datasources/prometheus.yml'"
    else
        print_warning "yamllint not available - skipping YAML validation"
        ((WARNING_CHECKS++))
    fi
    
    # Validate JSON dashboards
    for dashboard in "$MONITORING_DIR/grafana/dashboards"/*.json; do
        if [ -f "$dashboard" ]; then
            run_check_warn "Dashboard JSON syntax: $(basename "$dashboard")" "python -m json.tool '$dashboard' >/dev/null"
        fi
    done
    
    echo ""
}

# Service checks
check_services() {
    echo "=================================================="
    echo "  Service Structure Validation"
    echo "=================================================="
    
    # Check for benchmark directories in services
    local services=("user-service" "voucher-service" "product-service" "auth-service" "api-gateway" "order-service" "notification-service")
    
    for service in "${services[@]}"; do
        local service_dir="$PROJECT_ROOT/coupon-$service"
        if [ -d "$service_dir" ]; then
            run_check_warn "Benchmark directory exists for $service" "test -d '$service_dir/benchmark'"
            run_check_warn "Benchmark test file exists for $service" "test -f '$service_dir/benchmark/'*'_test.go'"
        else
            print_warning "Service directory not found: $service"
            ((WARNING_CHECKS++))
        fi
    done
    
    # Check shared libraries
    run_check "Shared metrics library exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/metrics/metrics.go'"
    run_check "Shared benchmark library exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/benchmark/framework.go'"
    run_check "Business metrics helper exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/metrics/business.go'"
    run_check "Benchmark exporter exists" "test -f '$PROJECT_ROOT/coupon-shared-libs/benchmark/exporter.go'"
    
    echo ""
}

# Network and port checks
check_network() {
    echo "=================================================="
    echo "  Network and Port Validation"
    echo "=================================================="
    
    # Check if ports are available
    local ports=(3000 8080 9090 9093 9100)
    
    for port in "${ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            print_warning "Port $port is already in use"
            ((WARNING_CHECKS++))
        else
            print_success "Port $port is available"
            ((PASSED_CHECKS++))
        fi
        ((TOTAL_CHECKS++))
    done
    
    # Check Docker network
    run_check_warn "Docker network coupon-network exists" "docker network ls | grep -q coupon-network"
    
    echo ""
}

# Go module checks
check_go_modules() {
    echo "=================================================="
    echo "  Go Module Validation"
    echo "=================================================="
    
    # Check if go.mod files exist and are valid
    local services=("shared-libs" "user-service" "voucher-service" "product-service" "auth-service" "api-gateway" "order-service" "notification-service")
    
    for service in "${services[@]}"; do
        local service_dir="$PROJECT_ROOT/coupon-$service"
        if [ -d "$service_dir" ]; then
            run_check_warn "Go module exists for $service" "test -f '$service_dir/go.mod'"
            if [ -f "$service_dir/go.mod" ]; then
                cd "$service_dir"
                run_check_warn "Go module is valid for $service" "go mod verify"
                cd "$PROJECT_ROOT"
            fi
        fi
    done
    
    echo ""
}

# Runtime checks (if services are running)
check_runtime() {
    echo "=================================================="
    echo "  Runtime Validation (if services are running)"
    echo "=================================================="
    
    # Check if monitoring stack is running
    if docker-compose -f "$PROJECT_ROOT/docker-compose.monitoring.yml" ps | grep -q "Up"; then
        print_status "Monitoring stack appears to be running - checking endpoints"
        
        run_check_warn "Prometheus endpoint accessible" "curl -s http://localhost:9090/-/ready"
        run_check_warn "Grafana endpoint accessible" "curl -s http://localhost:3000/api/health"
        run_check_warn "AlertManager endpoint accessible" "curl -s http://localhost:9093/-/ready"
        run_check_warn "Node Exporter endpoint accessible" "curl -s http://localhost:9100/metrics | head -1"
        run_check_warn "cAdvisor endpoint accessible" "curl -s http://localhost:8080/metrics | head -1"
        
        # Check Prometheus targets
        if curl -s http://localhost:9090/api/v1/targets | grep -q '"health":"up"'; then
            print_success "Some Prometheus targets are healthy"
            ((PASSED_CHECKS++))
        else
            print_warning "No healthy Prometheus targets found"
            ((WARNING_CHECKS++))
        fi
        ((TOTAL_CHECKS++))
        
    else
        print_warning "Monitoring stack is not running - skipping runtime checks"
        print_status "To start monitoring stack: make -f Makefile.monitoring monitoring-deploy"
        ((WARNING_CHECKS++))
    fi
    
    echo ""
}

# Permission checks
check_permissions() {
    echo "=================================================="
    echo "  Permission Validation"
    echo "=================================================="
    
    run_check "Deploy script is executable" "test -x '$PROJECT_ROOT/scripts/deploy-monitoring.sh'"
    run_check "Benchmark script is executable" "test -x '$PROJECT_ROOT/scripts/run-benchmarks.sh'"
    
    # Check if user can run Docker commands
    run_check "Docker permissions" "docker ps"
    
    echo ""
}

# Summary
print_summary() {
    echo "=================================================="
    echo "  Validation Summary"
    echo "=================================================="
    echo ""
    echo "Total Checks: $TOTAL_CHECKS"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Warnings: ${YELLOW}$WARNING_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    echo ""
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        if [ $WARNING_CHECKS -eq 0 ]; then
            echo -e "${GREEN}✓ All checks passed! Monitoring setup is ready.${NC}"
            exit 0
        else
            echo -e "${YELLOW}⚠ Setup is mostly ready with some warnings.${NC}"
            echo "Review the warnings above and address them if needed."
            exit 0
        fi
    else
        echo -e "${RED}✗ Setup validation failed.${NC}"
        echo "Please address the failed checks above before proceeding."
        exit 1
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "  Coupon System - Monitoring Setup Validation"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    check_file_structure
    check_configurations
    check_services
    check_network
    check_go_modules
    check_permissions
    check_runtime
    print_summary
}

# Handle script arguments
case "${1:-}" in
    "prerequisites")
        check_prerequisites
        ;;
    "files")
        check_file_structure
        ;;
    "config")
        check_configurations
        ;;
    "services")
        check_services
        ;;
    "network")
        check_network
        ;;
    "runtime")
        check_runtime
        ;;
    "permissions")
        check_permissions
        ;;
    *)
        main
        ;;
esac
