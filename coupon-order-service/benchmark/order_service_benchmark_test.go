package benchmark

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"

	orderv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/benchmark"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

var (
	orderGrpcSuite  *benchmark.GRPCBenchmarkSuite
	orderDbSuite    *benchmark.DatabaseBenchmarkSuite
	orderCacheSuite *benchmark.CacheBenchmarkSuite
	orderTestDB     *database.DB
	orderTestRedis  *redis.Client
	orderGenerator  *benchmark.TestDataGenerator
)

func TestMain(m *testing.M) {
	if err := setupOrderBenchmarkEnvironment(); err != nil {
		fmt.Printf("Failed to setup order benchmark environment: %v\n", err)
		os.Exit(1)
	}

	code := m.Run()
	cleanupOrder()
	os.Exit(code)
}

func setupOrderBenchmarkEnvironment() error {
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("order-service-benchmark")

	orderTestDB, err = database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	orderTestRedis = redis.NewClient(&cfg.Redis, logger, appMetrics)

	grpcConfig := &benchmark.GRPCBenchmarkConfig{
		ServiceAddress: "localhost:50056", // Order service gRPC port
		ServiceName:    "order-service",
		Timeout:        5 * time.Second,
	}

	orderGrpcSuite, err = benchmark.NewGRPCBenchmarkSuite(grpcConfig)
	if err != nil {
		return fmt.Errorf("failed to create gRPC benchmark suite: %w", err)
	}

	dbConfig := &benchmark.DatabaseBenchmarkConfig{
		ServiceName:    "order-service",
		DatabaseType:   "postgres",
		ConnectionPool: 10,
		QueryTimeout:   5 * time.Second,
	}
	orderDbSuite = benchmark.NewDatabaseBenchmarkSuite(dbConfig, orderTestDB.DB)

	orderCacheSuite = benchmark.NewCacheBenchmarkSuite("order-service", orderTestRedis)
	orderGenerator = benchmark.NewTestDataGenerator()

	return nil
}

func cleanupOrder() {
	if orderGrpcSuite != nil {
		orderGrpcSuite.Close()
	}
	if orderTestDB != nil {
		sqlDB, _ := orderTestDB.DB.DB()
		sqlDB.Close()
	}
	if orderTestRedis != nil {
		orderTestRedis.Close()
	}
}

// gRPC Endpoint Benchmarks
func BenchmarkOrderService_gRPC_CreateOrder(b *testing.B) {
	orderGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := orderv1.NewOrderServiceClient(conn)
		ctx = addOrderAuthMetadata(ctx)

		voucherCode := orderGenerator.GenerateVoucherCode()
		req := &orderv1.CreateOrderRequest{
			UserId: uint64(orderGenerator.GenerateUserID()),
			Items: []*orderv1.OrderItem{
				{
					ProductId: uint64(orderGenerator.GenerateProductID()),
					Quantity:  int32(orderGenerator.GenerateQuantity()),
					Price:     orderGenerator.GeneratePrice(),
				},
			},
			VoucherCode: &voucherCode,
		}

		_, err := client.CreateOrder(ctx, req)
		return err
	})
}

func BenchmarkOrderService_gRPC_GetOrder(b *testing.B) {
	orderGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := orderv1.NewOrderServiceClient(conn)
		ctx = addOrderAuthMetadata(ctx)

		req := &orderv1.GetOrderRequest{
			OrderId: uint64(orderGenerator.GenerateOrderID()),
		}

		_, err := client.GetOrder(ctx, req)
		return err
	})
}

func BenchmarkOrderService_gRPC_UpdateOrderStatus(b *testing.B) {
	orderGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := orderv1.NewOrderServiceClient(conn)
		ctx = addOrderAuthMetadata(ctx)

		statuses := []string{
			"PENDING",
			"PROCESSING",
			"SHIPPED",
			"DELIVERED",
		}

		req := &orderv1.UpdateOrderStatusRequest{
			OrderId: uint64(orderGenerator.GenerateOrderID()),
			Status:  statuses[orderGenerator.Rand().Intn(len(statuses))],
		}

		_, err := client.UpdateOrderStatus(ctx, req)
		return err
	})
}

func BenchmarkOrderService_gRPC_ListOrders(b *testing.B) {
	orderGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := orderv1.NewOrderServiceClient(conn)
		ctx = addOrderAuthMetadata(ctx)

		userId := uint64(orderGenerator.GenerateUserID())
		req := &orderv1.ListOrdersRequest{
			UserId: &userId,
		}

		_, err := client.ListOrders(ctx, req)
		return err
	})
}

func BenchmarkOrderService_gRPC_ListOrdersByVoucher(b *testing.B) {
	orderGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := orderv1.NewOrderServiceClient(conn)
		ctx = addOrderAuthMetadata(ctx)

		req := &orderv1.ListOrdersByVoucherRequest{
			VoucherId: uint64(orderGenerator.GenerateVoucherID()),
		}

		_, err := client.ListOrdersByVoucher(ctx, req)
		return err
	})
}

func BenchmarkOrderService_gRPC_GetUserOrderCount(b *testing.B) {
	orderGrpcSuite.BenchmarkUnaryCall(b, func(ctx context.Context, conn *grpc.ClientConn) error {
		client := orderv1.NewOrderServiceClient(conn)
		ctx = addOrderAuthMetadata(ctx)

		req := &orderv1.GetUserOrderCountRequest{
			UserId: uint64(orderGenerator.GenerateUserID()),
		}

		_, err := client.GetUserOrderCount(ctx, req)
		return err
	})
}

// Database Operation Benchmarks
func BenchmarkOrderService_Database_CreateOrder(b *testing.B) {
	orderDbSuite.BenchmarkTransaction(b, "create_order", func(ctx context.Context, tx *gorm.DB) error {
		// Create order
		orderData := map[string]interface{}{
			"user_id":      orderGenerator.GenerateUserID(),
			"total_amount": orderGenerator.GeneratePrice(),
			"status":       "PENDING",
			"voucher_code": orderGenerator.GenerateVoucherCode(),
			"created_at":   time.Now(),
			"updated_at":   time.Now(),
		}

		if err := tx.Table("orders").Create(orderData).Error; err != nil {
			return err
		}

		// Create order items
		for i := 0; i < 2; i++ {
			item := map[string]interface{}{
				"order_id":   orderData["id"],
				"product_id": orderGenerator.GenerateProductID(),
				"quantity":   orderGenerator.GenerateQuantity(),
				"price":      orderGenerator.GeneratePrice(),
				"created_at": time.Now(),
			}

			if err := tx.Table("order_items").Create(item).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

func BenchmarkOrderService_Database_GetOrderByID(b *testing.B) {
	orderDbSuite.BenchmarkQuery(b, "get_order_by_id", func(ctx context.Context, db *gorm.DB) error {
		orderID := orderGenerator.GenerateOrderID()
		var order struct {
			ID          int     `gorm:"column:id"`
			UserID      int     `gorm:"column:user_id"`
			TotalAmount float64 `gorm:"column:total_amount"`
			Status      string  `gorm:"column:status"`
			VoucherCode string  `gorm:"column:voucher_code"`
		}
		return db.Table("orders").Where("id = ?", orderID).First(&order).Error
	})
}

func BenchmarkOrderService_Database_ListUserOrders(b *testing.B) {
	orderDbSuite.BenchmarkQuery(b, "list_user_orders", func(ctx context.Context, db *gorm.DB) error {
		userID := orderGenerator.GenerateUserID()
		var orders []struct {
			ID          int       `gorm:"column:id"`
			TotalAmount float64   `gorm:"column:total_amount"`
			Status      string    `gorm:"column:status"`
			CreatedAt   time.Time `gorm:"column:created_at"`
		}
		return db.Table("orders").
			Where("user_id = ?", userID).
			Order("created_at DESC").
			Limit(20).Find(&orders).Error
	})
}

func BenchmarkOrderService_Database_UpdateOrderStatus(b *testing.B) {
	orderDbSuite.BenchmarkTransaction(b, "update_order_status", func(ctx context.Context, tx *gorm.DB) error {
		orderID := orderGenerator.GenerateOrderID()
		statuses := []string{"PENDING", "PROCESSING", "SHIPPED", "DELIVERED", "CANCELLED"}
		newStatus := statuses[orderGenerator.Rand().Intn(len(statuses))]

		// Update order status
		if err := tx.Table("orders").
			Where("id = ?", orderID).
			Update("status", newStatus).Error; err != nil {
			return err
		}

		// Create status history record
		history := map[string]interface{}{
			"order_id":   orderID,
			"status":     newStatus,
			"changed_at": time.Now(),
			"notes":      "Status updated via benchmark",
		}

		return tx.Table("order_status_history").Create(history).Error
	})
}

func BenchmarkOrderService_Database_GetOrderWithItems(b *testing.B) {
	orderDbSuite.BenchmarkQuery(b, "get_order_with_items", func(ctx context.Context, db *gorm.DB) error {
		orderID := orderGenerator.GenerateOrderID()

		// Get order
		var order struct {
			ID          int     `gorm:"column:id"`
			UserID      int     `gorm:"column:user_id"`
			TotalAmount float64 `gorm:"column:total_amount"`
			Status      string  `gorm:"column:status"`
		}
		if err := db.Table("orders").Where("id = ?", orderID).First(&order).Error; err != nil {
			return err
		}

		// Get order items
		var items []struct {
			ID        int     `gorm:"column:id"`
			ProductID int     `gorm:"column:product_id"`
			Quantity  int     `gorm:"column:quantity"`
			Price     float64 `gorm:"column:price"`
		}
		return db.Table("order_items").Where("order_id = ?", orderID).Find(&items).Error
	})
}

// Redis Cache Benchmarks
func BenchmarkOrderService_Cache_GetOrder(b *testing.B) {
	framework := benchmark.NewBenchmarkFramework(&benchmark.BenchmarkConfig{
		ServiceName:    "order-service",
		TestName:       "cache_get_order",
		MetricsEnabled: true,
	})

	operation := func(ctx context.Context) error {
		orderID := orderGenerator.GenerateOrderID()
		cacheKey := fmt.Sprintf("order:%d", orderID)

		// Try to get from cache first
		_, err := orderTestRedis.Get(ctx, cacheKey)
		if err == nil {
			return nil // Cache hit
		}

		// Cache miss - simulate database lookup and cache set
		orderData := orderGenerator.GenerateOrderData()
		return orderTestRedis.Set(ctx, cacheKey, orderData, 15*time.Minute)
	}

	framework.RunBenchmark(b, operation)
}

func BenchmarkOrderService_Cache_UserOrders(b *testing.B) {
	orderCacheSuite.BenchmarkCacheGet(b, func(ctx context.Context, key string) error {
		userID := orderGenerator.GenerateUserID()
		cacheKey := fmt.Sprintf("user_orders:%d", userID)
		_, err := orderTestRedis.Get(ctx, cacheKey)
		return err
	})
}

func BenchmarkOrderService_Cache_SetUserOrders(b *testing.B) {
	orderCacheSuite.BenchmarkCacheSet(b, func(ctx context.Context, key string, value interface{}) error {
		userID := orderGenerator.GenerateUserID()
		cacheKey := fmt.Sprintf("user_orders:%d", userID)
		ordersData := []map[string]interface{}{
			orderGenerator.GenerateOrderData(),
			orderGenerator.GenerateOrderData(),
		}
		return orderTestRedis.Set(ctx, cacheKey, ordersData, 10*time.Minute)
	})
}

// Concurrent Load Tests
func BenchmarkOrderService_ConcurrentLoad(b *testing.B) {
	scenario := &benchmark.LoadTestScenario{
		Name:        "order-service-mixed-load",
		Duration:    30 * time.Second,
		Concurrency: 12,
		RampUpTime:  5 * time.Second,
		Operations: []benchmark.LoadTestOperation{
			{
				Name:   "create_order",
				Weight: 25,
				Execute: func(ctx context.Context) error {
					ctx = addOrderAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50056",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := orderv1.NewOrderServiceClient(conn)
					voucherCode := orderGenerator.GenerateVoucherCode()
					req := &orderv1.CreateOrderRequest{
						UserId: uint64(orderGenerator.GenerateUserID()),
						Items: []*orderv1.OrderItem{
							{
								ProductId: uint64(orderGenerator.GenerateProductID()),
								Quantity:  int32(orderGenerator.GenerateQuantity()),
								Price:     orderGenerator.GeneratePrice(),
							},
						},
						VoucherCode: &voucherCode,
					}
					_, err = client.CreateOrder(ctx, req)
					return err
				},
			},
			{
				Name:   "get_order",
				Weight: 30,
				Execute: func(ctx context.Context) error {
					ctx = addOrderAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50056",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := orderv1.NewOrderServiceClient(conn)
					req := &orderv1.GetOrderRequest{
						OrderId: uint64(orderGenerator.GenerateOrderID()),
					}
					_, err = client.GetOrder(ctx, req)
					return err
				},
			},
			{
				Name:   "list_orders",
				Weight: 25,
				Execute: func(ctx context.Context) error {
					ctx = addOrderAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50056",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := orderv1.NewOrderServiceClient(conn)
					userId := uint64(orderGenerator.GenerateUserID())
					req := &orderv1.ListOrdersRequest{
						UserId: &userId,
					}
					_, err = client.ListOrders(ctx, req)
					return err
				},
			},
			{
				Name:   "update_order_status",
				Weight: 20,
				Execute: func(ctx context.Context) error {
					ctx = addOrderAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50056",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := orderv1.NewOrderServiceClient(conn)
					statuses := []string{
						"PENDING",
						"PROCESSING",
						"SHIPPED",
						"DELIVERED",
					}
					req := &orderv1.UpdateOrderStatusRequest{
						OrderId: uint64(orderGenerator.GenerateOrderID()),
						Status:  statuses[orderGenerator.Rand().Intn(len(statuses))],
					}
					_, err = client.UpdateOrderStatus(ctx, req)
					return err
				},
			},
			{
				Name:   "get_user_voucher_usage_count",
				Weight: 5,
				Execute: func(ctx context.Context) error {
					ctx = addOrderAuthMetadata(ctx)

					conn, err := grpc.DialContext(ctx, "localhost:50056",
						grpc.WithTransportCredentials(insecure.NewCredentials()))
					if err != nil {
						return err
					}
					defer conn.Close()

					client := orderv1.NewOrderServiceClient(conn)
					req := &orderv1.GetUserVoucherUsageCountRequest{
						UserId:    uint64(orderGenerator.GenerateUserID()),
						VoucherId: uint64(orderGenerator.GenerateVoucherID()),
					}
					_, err = client.GetUserVoucherUsageCount(ctx, req)
					return err
				},
			},
		},
	}

	runner := benchmark.NewLoadTestRunner(scenario)
	runner.RunLoadTest(b)
}

// Helper functions
func addOrderAuthMetadata(ctx context.Context) context.Context {
	md := metadata.New(map[string]string{
		"client-id":  "order-service-benchmark",
		"client-key": "benchmark-key",
	})
	return metadata.NewOutgoingContext(ctx, md)
}

// Helper functions for generating test data
func generateOrderUserID() int {
	return orderGenerator.Rand().Intn(1000) + 1
}
